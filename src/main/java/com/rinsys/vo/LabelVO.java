package com.rinsys.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/6/18 13:37
 */
@Data
public class LabelVO {

  /**
   * 标签类型 1: 物料标签 2：原材料标签 3：片材标签 4：客户标签
   */
  private String labelType;

  // 标签编号
  private String packageSn;

  // 生产日期
  private String produceDate;
  // 等级
  private String level;
  // 长度
  private String length;
  // 净重
  private String netWeight;
  // LOTNO
  private String lotNo;
  // 毛重
  private String grossWeight;
  // 打印数量
  private int packageCount;
  // 备注
  private String remark;
  // 长度(数量)
  private String lengthQuantity;
  // 订单号
  private String orderNo;
  // 机种
  private String machineType;
  // 片数
  private String pieceCount;

  // 明道云物料rowid
  private String materialRowId;

  private String userRowId;

  /**
   * 物料相关信息
   */

  // 物料编码
  private String materialSN;

  // 物料名称
  private String materialName;

  // 厚度
  private String thickness;

  // 型号
  private String model;

  // 版距
  private String plateDistance;

  // 宽幅
  private String width;

  // 原材料标签 流水号：开始
  private int startCount;

  // 原材料标签 流水号：结束
  private int endCount;

  private String validTime;

}
