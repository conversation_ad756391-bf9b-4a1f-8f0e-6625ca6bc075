package com.rinsys.service;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.rinsys.util.DateUtil;
import com.rinsys.util.RedisUtil;
import com.rinsys.vo.LabelVO;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javax.annotation.Resource;
import okhttp3.Call;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import static com.rinsys.util.DateUtil.fomatDate;


/**
 * <AUTHOR>
 * @date 2024/7/26 14:39
 */
@Service("mingDaoApiService")
public class MingDaoApiService {

  public static final String APP_KEY = "0e690a25b1b83f63";
  public static final String SIGN = "YjliNTdkYzAxOTg1ZGY2MjFmNDJjMGFkZGJhOWEwM2Q3YTU0MmZhZmFiYmUzMWI4ZmVhMDVhNWNlM2E3NmRiNQ==";
  public static final String BASE_URL = "http://192.168.112.104:8880/api/v2/open/worksheet/";
  public static final String API_LIST = "getFilterRows";
  public static final String API_DETAIL = "getRowByIdPost";

  public static final String API_ADD = "addRow";
  public static final String API_UPDATE = "editRow";

  public static final String SHEET_PACKAGE = "stock_history";
  // 全部包装信息
  public static final String VIEW_PACKAGE = "67510402a908e7cc7247d95b";

  public static final int PAGE_SIZE = 1000;
  public static final int PAGE_INDEX = 1;

  private final ObjectMapper objectMapper = new ObjectMapper();

  private final OkHttpClient okHttpClient;

  @Autowired
  public MingDaoApiService(OkHttpClient okHttpClient) {
    try {
      this.okHttpClient = okHttpClient;
    } catch (Exception e) {
      e.printStackTrace();
      throw e; // 重新抛出异常，防止程序静默失败
    }
  }

  @Resource
  private RedisUtil redisUtil;


  private JSONObject getWorksheetViewListByPage(String worksheetId, String viewId, String sortId,
      int pageIndex, List<ObjectNode> params) {

    ObjectNode paramJsonObject = objectMapper.createObjectNode();
    paramJsonObject.put("appKey", APP_KEY);
    paramJsonObject.put("sign", SIGN);
    paramJsonObject.put("worksheetId", worksheetId);
    paramJsonObject.put("viewId", viewId);
    paramJsonObject.put("pageSize", PAGE_SIZE);
    paramJsonObject.put("pageIndex", pageIndex);
    paramJsonObject.put("sortId", sortId);
    paramJsonObject.put("isAsc", false);

    if (params != null) {
      paramJsonObject.putPOJO("filters", params);
    }

    MediaType mediaType = MediaType.parse("application/json;charset=utf-8");
    RequestBody requestBody = RequestBody.create(mediaType, paramJsonObject.toString());

    Request request = new Request.Builder().url(BASE_URL + API_LIST).post(requestBody).build();
    JSONObject object = null;
    try {
      Call call = okHttpClient.newCall(request);
      Response response = call.execute();

      object = (JSONObject) JSONObject.parse(response.body().string());
      return object;
    } catch (Exception e) {
      e.printStackTrace();
    }
    return object;
  }

  private JSONObject getDetail(String worksheetId, String rowId) {
    ObjectNode resouceRequestParamJsonObject = objectMapper.createObjectNode();
    resouceRequestParamJsonObject.put("appKey", APP_KEY);
    resouceRequestParamJsonObject.put("sign", SIGN);
    resouceRequestParamJsonObject.put("worksheetId", worksheetId);
    resouceRequestParamJsonObject.put("rowId", rowId);

    MediaType mediaType = MediaType.parse("application/json;charset=utf-8");
    RequestBody requestBody = RequestBody.create(mediaType,
        resouceRequestParamJsonObject.toString());

    Request request = new Request.Builder().url(BASE_URL + API_DETAIL).post(requestBody).build();

    JSONObject object = null;
    String result = "";
    try {
      Call call = okHttpClient.newCall(request);
      Response response = call.execute();

      object = (JSONObject) JSONObject.parse(response.body().string());
      return object;
    } catch (Exception e) {
      e.printStackTrace();
    }
    return object;
  }

  public LabelVO getPackageDetail(String rowId) {
    JSONObject object = getDetail(SHEET_PACKAGE, rowId);
    object = object.getJSONObject("data");
    LabelVO labelVO = new LabelVO();
    // 包装类型
    String packageType = object.getString("package_type");
    switch (packageType) {
      case "物料标签":
        labelVO.setLabelType("1");
        break;
      case "原材料标签":
        labelVO.setLabelType("2");
        break;
      case "片材标签":
        labelVO.setLabelType("3");
        break;
      case "客户标签":
        labelVO.setLabelType("4");
        break;
    }
    // 包装编号
    labelVO.setPackageSn(object.getString("package_sn"));
    // 生产日期
    labelVO.setProduceDate(object.getString("product_date"));
    // 等级
    labelVO.setLevel(object.getString("level"));
    //包装数量
    labelVO.setLengthQuantity(object.getString("quantity"));
    // 打印备注
    labelVO.setRemark(object.getString("print_note"));
    // 打印数量, 默认:1
    labelVO.setPackageCount(1);
    // 片材标签片数处理
    labelVO.setPieceCount(labelVO.getLengthQuantity());
    // 订单号
    labelVO.setOrderNo(object.getString("order_number"));
    // 机种
    labelVO.setMachineType(object.getString("machine_type"));

    // 物料相关信息获取
    JSONArray materials = JSONArray.parseArray(object.getString("ref_material"));

    String materialRowId = materials.size() > 0 ? materials.getJSONObject(0).getString("sid") : "";
    labelVO.setMaterialRowId(materialRowId);
    // 物料编码
    labelVO.setMaterialSN(object.getString("material_no"));
    // 物料名称
    labelVO.setMaterialName(object.getString("material_name"));
    // 厚度
    labelVO.setThickness(object.getString("material_thickness"));
    // 规格
    labelVO.setModel(object.getString("material_spec"));
    // 版距
    labelVO.setPlateDistance(object.getString("material_pagespace"));
    // 宽度
    labelVO.setWidth(object.getString("material_width"));
    // 毛重
    labelVO.setGrossWeight(object.getString("gross_weight"));
    // 静重
    labelVO.setNetWeight(object.getString("net_weight"));
    // 生产Lot No
    labelVO.setLotNo(object.getString("lot_no"));
    // 长度
    labelVO.setLength(object.getString("length"));

    return labelVO;
  }

  private void addRecord(String worksheetId, List<ObjectNode> values) {
    ObjectNode actionParamJsonObject = objectMapper.createObjectNode();
    actionParamJsonObject.put("appKey", APP_KEY);
    actionParamJsonObject.put("sign", SIGN);
    actionParamJsonObject.put("worksheetId", worksheetId);

    actionParamJsonObject.putPOJO("controls", values);

    MediaType mediaType = MediaType.parse("application/json;charset=utf-8");
    RequestBody requestBody = RequestBody.create(mediaType, actionParamJsonObject.toString());
    Request request = new Request.Builder().url(BASE_URL + API_ADD).post(requestBody).build();

    try {
      Call call = okHttpClient.newCall(request);
      call.execute();

    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  public void syncLotNos() {

    JSONObject object = getWorksheetViewListByPage(SHEET_PACKAGE, VIEW_PACKAGE, "ctime",
        PAGE_INDEX,
        null);
    object = (JSONObject) object.get("data");
    int total = object.getInteger("total");
    int pageSize = (total % PAGE_SIZE == 0) ? total / PAGE_SIZE : total / PAGE_SIZE + 1;

    Set<String> lotNos = new HashSet<>();

    for (int i = 1; i <= pageSize; i++) {
      object = getWorksheetViewListByPage(SHEET_PACKAGE, VIEW_PACKAGE, "ctime", i, null);
      object = (JSONObject) object.get("data");

      if (object == null || object.get("rows") == null) {
        continue;
      }

      System.out.println("page:" + i);

      if (object != null && object.get("rows") != null) {
        JSONArray array = (JSONArray) object.get("rows");

        if (array == null || array.size() == 0) {
          return;
        } else {
          for (int j = 0; j < array.size(); j++) {
            JSONObject jsonObject = array.getJSONObject(j);

            String lotNo = jsonObject.getString("lot_no");
            if (!StringUtils.isEmpty(lotNo)) {
              redisUtil.addToList("WMS_LOT_NOS", lotNo);
            }
          }

        }
      }

    }
  }

  public void addPackage(LabelVO param) {
    List<ObjectNode> params = new ArrayList();

    // 物料
    ObjectNode field1 = objectMapper.createObjectNode();
    field1.put("controlId", "ref_material");
    field1.put("value", param.getMaterialRowId());

    // 包装编号_API
    ObjectNode field2 = objectMapper.createObjectNode();
    field2.put("controlId", "package_sn_api");
    field2.put("value", param.getPackageSn());

    // 生产lot_no
    ObjectNode field3 = objectMapper.createObjectNode();
    field3.put("controlId", "lot_no");
    field3.put("value", param.getLotNo());

    // 包装数量
    ObjectNode field4 = objectMapper.createObjectNode();
    field4.put("controlId", "quantity");
    field4.put("value", param.getLengthQuantity());

    // 净重
    ObjectNode field5 = objectMapper.createObjectNode();
    field5.put("controlId", "net_weight");
    field5.put("value", param.getNetWeight());

    // 毛重
    ObjectNode field6 = objectMapper.createObjectNode();
    field6.put("controlId", "gross_weight");
    field6.put("value", param.getGrossWeight());

    // 包装类型
    ObjectNode field7 = objectMapper.createObjectNode();
    field7.put("controlId", "package_type");
    switch (param.getLabelType()) {
      case "1":
        field7.put("value", "物料标签");
        break;
      case "2":
        field7.put("value", "原材料标签");
        break;
      case "3":
        field7.put("value", "片材标签");
        break;
      case "4":
        field7.put("value", "客户标签");
        break;
    }

    // 等级
    ObjectNode field8 = objectMapper.createObjectNode();
    field8.put("controlId", "level");
    field8.put("value", param.getLevel());

    // 打印备注
    ObjectNode field9 = objectMapper.createObjectNode();
    field9.put("controlId", "print_note");
    field9.put("value", param.getRemark());

    // 机种
    ObjectNode field10 = objectMapper.createObjectNode();
    field10.put("controlId", "machine_type");
    field10.put("value", param.getMachineType());

    // 订单号
    ObjectNode field11 = objectMapper.createObjectNode();
    field11.put("controlId", "order_number");
    field11.put("value", param.getOrderNo());

    // 生产日期
    ObjectNode field12 = objectMapper.createObjectNode();
    field12.put("controlId", "product_date");
    // data.getProduceDate() 不存在就取packageSn前8位
    if (StringUtils.isEmpty(param.getProduceDate())) {
      param.setProduceDate(DateUtil.formatDateString(param.getPackageSn().substring(0, 8)));
    }
    field12.put("value", param.getProduceDate());

    // 创建人
    ObjectNode field13 = objectMapper.createObjectNode();
    field13.put("controlId", "user_rowid");
    field13.put("value", param.getUserRowId());

    // 创建时间
    ObjectNode field14 = objectMapper.createObjectNode();
    field14.put("controlId", "create_time");
    field14.put("value", DateUtil.getTime());

    // 有效时间
    ObjectNode field15 = objectMapper.createObjectNode();
    field15.put("controlId", "vaild_time");
    field15.put("value", param.getValidTime());

    params.add(field1);
    params.add(field2);
    params.add(field3);
    params.add(field4);
    params.add(field5);
    params.add(field6);
    params.add(field7);
    params.add(field8);
    params.add(field9);
    params.add(field10);
    params.add(field11);
    params.add(field12);
    params.add(field13);
    params.add(field14);

    addRecord(SHEET_PACKAGE, params);
  }

  public List<String> getLotNos() {
    return redisUtil.getListValue("WMS_LOT_NOS");
  }

  public void deleteLotNo(String lotNo) {
    redisUtil.removeFromList("WMS_LOT_NOS", lotNo);
  }

}
