package com.rinsys.service.impl;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.client.j2se.MatrixToImageConfig;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.datamatrix.encoder.SymbolShapeHint;
import com.google.zxing.qrcode.QRCodeWriter;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import com.itextpdf.text.Chunk;
import com.itextpdf.text.Document;
import com.itextpdf.text.Element;
import com.itextpdf.text.Font;
import com.itextpdf.text.Image;
import com.itextpdf.text.Paragraph;
import com.itextpdf.text.Phrase;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.rinsys.config.Const;
import com.rinsys.service.LabelService;
import com.rinsys.service.MingDaoApiService;
import com.rinsys.util.DateUtil;
import com.rinsys.util.LengthCalculatorUtil;
import com.rinsys.util.LotNoUtil;
import com.rinsys.util.PackageNoUtil;
import com.rinsys.util.RedisUtil;
import com.rinsys.vo.LabelVO;
import java.awt.Color;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2024/6/18 14:53
 */
@Service
public class LabelServiceImpl implements LabelService {

  @Override
  public LabelVO getLabelInfoForPrint(LabelVO param) {
    return null;
  }

  @Resource
  private RedisUtil redisUtil;

  @Resource
  private PackageNoUtil packageNoUtil;

  @Resource
  private LotNoUtil lotNoUtil;

  @Resource(name = "mingDaoApiService")
  private MingDaoApiService mingDaoApiService;


  @Override
  public void printLabel(List<LabelVO> param, HttpServletResponse response) {
    switch (param.get(0).getLabelType()) {
      case "1":
        // 打印物料标签
        printMaterialLabel(param, response);
        break;
      case "2":
        // 打印原材料标签
        printRawMaterialLabel(param, response);
        break;
      case "3":
        // 打印片材标签
        printSheetLabel(param, response);
        break;
      case "4":
        // 打印客户标签
        printClientLabel(param, response);
        break;
    }

  }

  /**
   * 打印物料标签
   *
   * @param param
   * @param response
   */
  void printMaterialLabel(List<LabelVO> param, HttpServletResponse response) {
    try {
      response.setCharacterEncoding("UTF-8");
      response.setContentType("application/pdf");

      Rectangle pageSize = new Rectangle(280F, 280F);
      Document document = new Document(pageSize, 10, 10, 10, 10);
      PdfWriter writer = PdfWriter.getInstance(document, response.getOutputStream());
      BaseFont baseFont = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",
          BaseFont.NOT_EMBEDDED);
      Font font = new Font(baseFont, 11, Font.NORMAL);
      Font font2 = new Font(baseFont, 9, Font.NORMAL);
      Font font3 = new Font(baseFont, 9, Font.NORMAL);

      Font producttypeFont = new Font(baseFont, 11, Font.NORMAL);

      ServletOutputStream out = response.getOutputStream();

      document.open();
      int columnHeight = 20;

      for (LabelVO item : param) {
        int packageCount = item.getPackageCount();

        // lotNo不能重复 (redis 首次一次性拉取)
        if (lotNoUtil.isExistLotNo(item.getLotNo())) {
          return;
        }

        String packageNo = packageNoUtil.getPackageNo();
        List<LabelVO> labelList = new ArrayList<>();
        for (int i = 0; i < packageCount; i++) {
          LabelVO data = new LabelVO();
          BeanUtils.copyProperties(item, data);
          data.setPackageSn(packageNo);

          PdfPTable table1 = new PdfPTable(1);
          table1.setKeepTogether(true);

          PdfPCell cell0 = new PdfPCell();

          Paragraph p = new Paragraph(Const.LABEL_TITLE, font);

          p.setAlignment(1);
          p.setSpacingBefore(15f);
          cell0.setHorizontalAlignment(PdfPCell.ALIGN_CENTER);
          cell0.setVerticalAlignment(PdfPCell.ALIGN_MIDDLE);//error
          cell0.setPaddingTop(-2f);//把字垂直居中
          cell0.setPaddingBottom(8f);//把字垂直居中
          cell0.addElement(p);
          cell0.setBorder(0);

          cell0.setNoWrap(true);
          table1.setWidthPercentage(100);
          //        table1.setKeepTogether(true);
          table1.addCell(cell0);
          table1.setSpacingBefore(20);
          PdfPTable pTable = new PdfPTable(table1);
          document.add(pTable);

          if ("窗膜".equalsIgnoreCase(data.getMaterialName())) {

            PdfPTable table = new PdfPTable(2);
            table.setWidths(new int[]{60, 200});
            table.setWidthPercentage(100);
            table.setSpacingBefore(10);
            table.setTotalWidth(100);
            table.setKeepTogether(true);
            Paragraph materialName = new Paragraph("产品品名", font);
            PdfPCell cellTemp = new PdfPCell(materialName);
            cellTemp.setFixedHeight(columnHeight);
            table.addCell(cellTemp);

            Paragraph name = new Paragraph(data.getMaterialName(), font);
            cellTemp = new PdfPCell(name);
            cellTemp.setFixedHeight(columnHeight);
            table.addCell(cellTemp);

            table.setSpacingBefore(0);
            Paragraph typeName = new Paragraph("产品型号", font);
            cellTemp = new PdfPCell(typeName);
            cellTemp.setFixedHeight(columnHeight);
            table.addCell(cellTemp);

            Paragraph typeNameValue = new Paragraph(data.getModel(), font);
            cellTemp = new PdfPCell(typeNameValue);
            cellTemp.setFixedHeight(columnHeight);
            table.addCell(cellTemp);
            document.add(table);

            table = new PdfPTable(4);

            table.setWidthPercentage(100);

            table.setTotalWidth(100);
            table.setKeepTogether(true);

            table.setWidths(new int[]{60, 80, 50, 70});

            //line 1
            Paragraph specTitle = new Paragraph("宽幅", font);

            cellTemp = new PdfPCell(specTitle);
            cellTemp.setFixedHeight(columnHeight);
            table.addCell(cellTemp);

            Chunk c1 = new Chunk(data.getWidth(), font);
            Chunk c2 = new Chunk("   mm", font);
            Phrase phrase = new Phrase();
            phrase.add(c1);
            phrase.add(c2);

            cellTemp = new PdfPCell(phrase);
            cellTemp.setHorizontalAlignment(0);
            cellTemp.setFixedHeight(columnHeight);
            table.addCell(cellTemp);

            Paragraph materialSn = new Paragraph("物料编码", font);

            cellTemp = new PdfPCell(materialSn);
            cellTemp.setFixedHeight(columnHeight);
            table.addCell(cellTemp);

            Paragraph materialSN = new Paragraph(data.getMaterialSN(), font);

            cellTemp = new PdfPCell(materialSN);
            cellTemp.setFixedHeight(columnHeight);
            table.addCell(cellTemp);

            document.add(table);

            //table 3 start

            PdfPTable table3 = new PdfPTable(2);
            table3.setWidthPercentage(100);
            table3.setTotalWidth(260);
            table3.setKeepTogether(true);
            table3.setSpacingBefore(0);
            table3.setWidths(new int[]{140, 120});

            PdfPTable table2 = new PdfPTable(2);
            table2.setWidthPercentage(100);
            table2.setTotalWidth(140);
            table2.setKeepTogether(true);
            table2.setSpacingBefore(0);
            //line 3
            table2.setWidths(new int[]{60, 80});

            Paragraph length = new Paragraph("小卷长度", font);

            cellTemp = new PdfPCell(length);
            cellTemp.setFixedHeight(columnHeight);
            table2.addCell(cellTemp);

            // 长度=数量*1000/宽幅
            String lengthStr = LengthCalculatorUtil.calculateLengthAsString(
                data.getLengthQuantity(),
                data.getWidth());
            Paragraph lengthValue = new Paragraph(lengthStr, font);

            cellTemp = new PdfPCell(lengthValue);
            cellTemp.setFixedHeight(columnHeight);
            table2.addCell(cellTemp);

            Paragraph netWeightName = new Paragraph("净        重", font);

            cellTemp = new PdfPCell(netWeightName);
            cellTemp.setFixedHeight(columnHeight);
            table2.addCell(cellTemp);

            c1 = new Chunk(data.getNetWeight(), font);
            c2 = new Chunk("   KG", font);
            phrase = new Phrase();
            phrase.add(c1);
            phrase.add(c2);

            cellTemp = new PdfPCell(phrase);
            cellTemp.setFixedHeight(columnHeight);
            table2.addCell(cellTemp);

            //table 2 line 2
            Paragraph grossWeightName = new Paragraph("毛        重", font);
            cellTemp = new PdfPCell(grossWeightName);
            cellTemp.setFixedHeight(columnHeight);
            table2.addCell(cellTemp);

            //          Paragraph grossWeight = new Paragraph(data.getString("grossWeight"), font);
            c1 = new Chunk(data.getGrossWeight(), font);
            c2 = new Chunk("   KG", font);
            phrase = new Phrase();
            phrase.add(c1);
            phrase.add(c2);

            cellTemp = new PdfPCell(phrase);
            cellTemp.setFixedHeight(columnHeight);
            table2.addCell(cellTemp);

            Paragraph dateTitle = new Paragraph("生产日期", font);
            cellTemp = new PdfPCell(dateTitle);
            cellTemp.setFixedHeight(columnHeight);
            table2.addCell(cellTemp);
            // data.getProduceDate() 不存在就取packageSn前8位
            if (StringUtils.isEmpty(data.getProduceDate())) {
              data.setProduceDate(DateUtil.formatDateString(data.getPackageSn().substring(0, 8)));
            }
            Paragraph date = new Paragraph(data.getProduceDate(), font);
            cellTemp = new PdfPCell(date);
            cellTemp.setFixedHeight(columnHeight);
            table2.addCell(cellTemp);

            PdfPCell cell = new PdfPCell(table2);
            cell.setPadding(0);
            table3.addCell(cell);
            int width = 110;
            int height = 70;
            String contents = "RBZ#" + data.getPackageSn()  //包装编号
                + "$$" + data.getMaterialSN()
                + "$$" + data.getLotNo()
                + "$$" + data.getLevel();

            contents = new String(contents.getBytes("UTF-8"), "ISO-8859-1");

            // HashMap<EncodeHintType, Object> map = new HashMap<EncodeHintType, Object>();
            // map.put(EncodeHintType.CHARACTER_SET, "UTF-8");
            // map.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
            // map.put(EncodeHintType.MARGIN, 2);

            // MatrixToImageConfig con = new MatrixToImageConfig(Color.BLACK.getRGB(),
            //     Color.WHITE.getRGB());

            // BitMatrix bitMatrix =
            //     new MultiFormatWriter().encode(contents, BarcodeFormat.DATA_MATRIX, width, height,
            //         map);

            // BufferedImage codeImage = MatrixToImageWriter.toBufferedImage(bitMatrix, con);

            // ByteArrayOutputStream fos = new ByteArrayOutputStream();
            // ImageIO.write(codeImage, "jpg", fos);

            // Image cellimg = Image.getInstance(fos.toByteArray());

            // PdfPCell cell4 = new PdfPCell(cellimg, true);
            // Generate QR code
            QRCodeWriter qrCodeWriter = new QRCodeWriter();
            Map<EncodeHintType, Object> hints = new HashMap<>();
            hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
            BitMatrix bitMatrix = qrCodeWriter.encode(contents, BarcodeFormat.QR_CODE, width, height, hints);

            // Convert BitMatrix to Image
            java.awt.Image awtImage = MatrixToImageWriter.toBufferedImage(bitMatrix);
            Image image = Image.getInstance(awtImage, null);

            PdfPCell cell4 = new PdfPCell(image, true);
            cell4.setFixedHeight(80);
            cell4.setPaddingTop(1);
            cell4.setPaddingBottom(1);
            cell4.setPaddingLeft(1);
            cell4.setPaddingRight(1);

            cell4.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell4.setVerticalAlignment(Element.ALIGN_MIDDLE);

            table3.addCell(cell4);
            document.add(table3);

            table = new PdfPTable(1);
            //          table.setWidths(new int[]{60, 200});
            table.setWidthPercentage(100);
            table.setSpacingBefore(0);
            table.setTotalWidth(100);
            table.setKeepTogether(true);

            c1 = new Chunk("备注", font);

            c2 = new Chunk(data.getRemark(), font);
            phrase = new Phrase();
            phrase.add(c1);
            phrase.add(Chunk.NEWLINE);
            phrase.add(c2);

            cellTemp = new PdfPCell(phrase);
            cellTemp.setFixedHeight(columnHeight * 4);
            //          cellTemp.setVerticalAlignment(PdfPCell.ALIGN_MIDDLE);
            table.addCell(cellTemp);
            document.add(table);

            labelList.add(data);

            // 回写明道云
//            mingDaoApiService.addPackage(data);

          } else {
            PdfPTable table = new PdfPTable(4);
            table.setWidths(new int[]{35, 105, 50, 70});

            table.setWidthPercentage(100);
            table.setSpacingBefore(10);
            table.setTotalWidth(100);
            table.setKeepTogether(true);
            Paragraph materialName = new Paragraph("名称", font);
            PdfPCell cellTemp = new PdfPCell(materialName);
            cellTemp.setFixedHeight(columnHeight);
            table.addCell(cellTemp);

            Paragraph name = new Paragraph(data.getMaterialName(), font);
            cellTemp = new PdfPCell(name);
            cellTemp.setFixedHeight(columnHeight);
            table.addCell(cellTemp);

            Paragraph dateTitle = new Paragraph("生产日期", font);
            cellTemp = new PdfPCell(dateTitle);
            cellTemp.setFixedHeight(columnHeight);
            table.addCell(cellTemp);
            // data.getProduceDate() 不存在就取packageSn前8位
            if (StringUtils.isEmpty(data.getProduceDate())) {
              data.setProduceDate(DateUtil.formatDateString(data.getPackageSn().substring(0, 8)));
            }
            Paragraph date = new Paragraph(data.getProduceDate(), font);
            cellTemp = new PdfPCell(date);
            cellTemp.setFixedHeight(columnHeight);
            table.addCell(cellTemp);

            document.add(table);

            table = new PdfPTable(2);

            table.setWidthPercentage(100);

            table.setTotalWidth(100);
            table.setKeepTogether(true);

            table.setWidths(new int[]{35, 225});

            //line 1
            Paragraph specTitle = new Paragraph("型号", font);

            cellTemp = new PdfPCell(specTitle);
            cellTemp.setFixedHeight(columnHeight);
            table.addCell(cellTemp);

            Paragraph spec = new Paragraph(data.getModel(), producttypeFont);

            cellTemp = new PdfPCell(spec);
            cellTemp.setFixedHeight(columnHeight);
            cellTemp.setVerticalAlignment(PdfPCell.ALIGN_MIDDLE);
            table.addCell(cellTemp);

            PdfPTable table7 = new PdfPTable(4);
            table7.setWidths(new int[]{35, 105, 50, 70});
            table7.setWidthPercentage(100);
            table7.setSpacingBefore(0);
            table7.setTotalWidth(100);

            //line2

            Paragraph thicknessName = new Paragraph("厚度", font);
            PdfPCell cell2 = new PdfPCell(thicknessName);
            cell2.setFixedHeight(columnHeight);
            table7.addCell(cell2);
            Chunk c1 = new Chunk(data.getThickness(), font);
            Chunk c2 = new Chunk("   um", font);
            Phrase phrase = new Phrase();
            phrase.add(c1);
            phrase.add(c2);

            Paragraph thickness = new Paragraph();
            thickness.add(phrase);
            thickness.setAlignment(0);

            PdfPCell cell3 = new PdfPCell(thickness);
            cell3.setFixedHeight(columnHeight);
            cell3.setHorizontalAlignment(0);
            table7.addCell(cell3);

            Paragraph materialSn = new Paragraph("物料编码", font);

            cellTemp = new PdfPCell(materialSn);
            cellTemp.setFixedHeight(columnHeight);
            table7.addCell(cellTemp);

            Paragraph materialSN = new Paragraph(data.getMaterialSN(), font);

            cellTemp = new PdfPCell(materialSN);
            cellTemp.setFixedHeight(columnHeight);
            table7.addCell(cellTemp);

            PdfPTable table5 = new PdfPTable(2);
            table5.setWidths(new int[]{35, 225});

            table5.setWidthPercentage(100);
            table5.setSpacingBefore(0);
            table5.setTotalWidth(100);
            table5.setKeepTogether(true);

            Paragraph lotNoName = new Paragraph("Lot No", font);
            PdfPCell lotNoCellTemp = new PdfPCell(lotNoName);
            cellTemp.setFixedHeight(columnHeight);
            table5.addCell(lotNoCellTemp);

            Paragraph lotNo = new Paragraph(data.getLotNo(), font2);
            font2.setSize(13);
            cellTemp = new PdfPCell(lotNo);
            cellTemp.setFixedHeight(columnHeight);
            table5.addCell(cellTemp);

            //line3
            PdfPTable table6 = new PdfPTable(4);
            table6.setWidths(new int[]{35, 105, 50, 70});

            table6.setWidthPercentage(100);
            table6.setSpacingBefore(0);
            table6.setTotalWidth(100);
            table6.setKeepTogether(true);
            Paragraph widthTitle = new Paragraph("宽幅", font);
            PdfPCell cellTemp4 = new PdfPCell(widthTitle);
            cellTemp4.setFixedHeight(columnHeight);
            table6.addCell(cellTemp4);
            c1 = new Chunk(data.getWidth(), font);
            c2 = new Chunk("   mm", font);
            phrase = new Phrase();
            phrase.add(c1);
            phrase.add(c2);

            cellTemp = new PdfPCell(phrase);
            cellTemp.setHorizontalAlignment(0);
            cellTemp.setFixedHeight(columnHeight);
            table6.addCell(cellTemp);

            Paragraph validPeriodTitle = new Paragraph("有效期限", font);
            cellTemp = new PdfPCell(validPeriodTitle);
            cellTemp.setFixedHeight(columnHeight);
            table6.addCell(cellTemp);
//            Paragraph purchaseNo = new Paragraph("", font);
            Paragraph purchaseNo = new Paragraph(data.getValidTime(), font);
            cellTemp = new PdfPCell(purchaseNo);
            cellTemp.setFixedHeight(columnHeight);
            table6.addCell(cellTemp);

            //
            document.add(table);
            document.add(table7);
            document.add(table5);
            document.add(table6);

            //table 3 start

            PdfPTable table3 = new PdfPTable(2);
            table3.setWidthPercentage(100);
            table3.setTotalWidth(260);
            table3.setKeepTogether(true);
            table3.setSpacingBefore(0);
            table3.setWidths(new int[]{140, 120});

            PdfPTable table2 = new PdfPTable(2);
            table2.setWidthPercentage(100);
            table2.setTotalWidth(140);
            table2.setKeepTogether(true);
            table2.setSpacingBefore(0);
            //line 3
            table2.setWidths(new int[]{35, 105});

            //line 5

            Paragraph lengthName = new Paragraph("长度", font);
            PdfPCell lengthCellName = new PdfPCell(lengthName);
            lengthCellName.setFixedHeight(columnHeight);
            table2.addCell(lengthCellName);

            // 长度=数量*1000/宽幅
            String length = LengthCalculatorUtil.calculateLengthAsString(data.getLengthQuantity(),
                data.getWidth());
            c1 = new Chunk(length, font);
            c2 = new Chunk("   m", font);
            phrase = new Phrase();
            phrase.add(c1);
            phrase.add(c2);
            PdfPCell lengthCell = new PdfPCell(phrase);
            lengthCell.setFixedHeight(columnHeight);
            lengthCell.setHorizontalAlignment(0);
            table2.addCell(lengthCell);

            Paragraph quantityName = new Paragraph("数量", font);
            PdfPCell quantityCellName = new PdfPCell(quantityName);
            quantityCellName.setFixedHeight(columnHeight);
            table2.addCell(quantityCellName);

            c1 = new Chunk(data.getLengthQuantity(), font);
            c2 = new Chunk("   M", font);
            Chunk c3 = new Chunk("2", font3);
            c3.setTextRise(4);
            phrase = new Phrase();
            phrase.add(c1);
            phrase.add(c2);
            phrase.add(c3);

            PdfPCell quantityCell = new PdfPCell(phrase);
            quantityCell.setFixedHeight(columnHeight);
            quantityCell.setHorizontalAlignment(0);
            table2.addCell(quantityCell);

            Paragraph pageSpaceName = new Paragraph("版距", font);
            cellTemp = new PdfPCell(pageSpaceName);
            cellTemp.setFixedHeight(columnHeight);
            table2.addCell(cellTemp);

            Paragraph pageSpace = new Paragraph(data.getPlateDistance(), font);
            cellTemp = new PdfPCell(pageSpace);
            cellTemp.setFixedHeight(columnHeight);
            table2.addCell(cellTemp);

            Paragraph netWeightName = new Paragraph("净重", font);

            cellTemp = new PdfPCell(netWeightName);
            cellTemp.setFixedHeight(columnHeight);
            table2.addCell(cellTemp);
            c1 = new Chunk(data.getNetWeight(), font);
            c2 = new Chunk("   KG", font);
            phrase = new Phrase();
            phrase.add(c1);
            phrase.add(c2);

            cellTemp = new PdfPCell(phrase);
            cellTemp.setFixedHeight(columnHeight);
            table2.addCell(cellTemp);

            //table 2 line 2
            Paragraph grossWeightName = new Paragraph("毛重", font);
            cellTemp = new PdfPCell(grossWeightName);
            cellTemp.setFixedHeight(columnHeight);
            table2.addCell(cellTemp);
            c1 = new Chunk(data.getGrossWeight(), font);
            c2 = new Chunk("   KG", font);
            phrase = new Phrase();
            phrase.add(c1);
            phrase.add(c2);

            cellTemp = new PdfPCell(phrase);
            cellTemp.setFixedHeight(columnHeight);
            table2.addCell(cellTemp);

            PdfPCell cell = new PdfPCell(table2);
            cell.setPadding(0);
            table3.addCell(cell);
            int width = 140;
            int height = 150;
            String contents = "RBZ#" + data.getPackageSn()  //包装编号
                + "$$" + data.getMaterialSN()
                + "$$" + data.getMaterialName() //产品名称
                + "$$" + data.getModel() //产品型号
                + "$$" + length + "$$" + data.getThickness()
                + "$$" + data.getLengthQuantity()
                + "$$" + data.getLotNo()
                + "$$" + data.getLevel();

            contents = new String(contents.getBytes("UTF-8"), "ISO-8859-1");

            // HashMap<EncodeHintType, Object> map = new HashMap<EncodeHintType, Object>();
            // map.put(EncodeHintType.CHARACTER_SET, "UTF-8");
            // map.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
            // map.put(EncodeHintType.MARGIN, 2);

            // MatrixToImageConfig con = new MatrixToImageConfig(Color.BLACK.getRGB(),
            //     Color.WHITE.getRGB());

            // BitMatrix bitMatrix =
            //     new MultiFormatWriter().encode(contents, BarcodeFormat.DATA_MATRIX, width, height,
            //         map);

            // BufferedImage codeImage = MatrixToImageWriter.toBufferedImage(bitMatrix, con);

            // ByteArrayOutputStream fos = new ByteArrayOutputStream();
            // ImageIO.write(codeImage, "jpg", fos);

            // Image cellimg = Image.getInstance(fos.toByteArray());

            // PdfPCell cell4 = new PdfPCell(cellimg, true);
            // Generate QR code
            QRCodeWriter qrCodeWriter = new QRCodeWriter();
            Map<EncodeHintType, Object> hints = new HashMap<>();
            hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
            BitMatrix bitMatrix = qrCodeWriter.encode(contents, BarcodeFormat.QR_CODE, width, height, hints);

            // Convert BitMatrix to Image
            java.awt.Image awtImage = MatrixToImageWriter.toBufferedImage(bitMatrix);
            Image image = Image.getInstance(awtImage, null);

            PdfPCell cell4 = new PdfPCell(image, true);
            cell4.setFixedHeight(100);
            cell4.setPaddingTop(1);
            cell4.setPaddingBottom(1);
            cell4.setPaddingLeft(0);
            cell4.setPaddingRight(0);

            cell4.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell4.setVerticalAlignment(Element.ALIGN_MIDDLE);

            table3.addCell(cell4);
            document.add(table3);

            //一维码
            PdfPTable table4 = new PdfPTable(1);
            table4.setSpacingBefore(0);
            table4.setKeepTogether(true);
            table4.setWidthPercentage(100);

            String codeContents = data.getLotNo();

            if (!StringUtils.isEmpty(codeContents)) {
              Image barCellImg = Image.getInstance(generateBarCode(codeContents, 15).toByteArray());

              PdfPCell cell5 = new PdfPCell(barCellImg, true);
              cell5.setFixedHeight(20);
              cell5.setPaddingTop(2);
              cell5.setPaddingLeft(20);
              cell5.setPaddingRight(20);
              cell5.setPaddingBottom(2);
              cell5.setHorizontalAlignment(Element.ALIGN_CENTER);
              cell5.setVerticalAlignment(Element.ALIGN_MIDDLE);

              table4.addCell(cell5);
            } else {
              Paragraph emptylotNo = new Paragraph("", font);
              PdfPCell cell5 = new PdfPCell(emptylotNo);
              cell5.setFixedHeight(20);
              cell5.setPaddingTop(2);
              cell5.setPaddingLeft(20);
              cell5.setPaddingRight(20);
              cell5.setPaddingBottom(2);
              cell5.setHorizontalAlignment(Element.ALIGN_CENTER);
              cell5.setVerticalAlignment(Element.ALIGN_MIDDLE);

              table4.addCell(cell5);
            }

            document.add(table4);

            labelList.add(data);
            // 回写明道云
//            mingDaoApiService.addPackage(data);
          }
        }
        // 回写明道云
        mingDaoApiService.addPackage(labelList.get(0));
      }
      lotNoUtil.addLotNo(param.get(0).getLotNo());
      document.close();

      writer.flush();
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  /**
   * 打印原材料标签
   *
   * @param param
   * @param response
   */
  void printRawMaterialLabel(List<LabelVO> param, HttpServletResponse response) {
    try {
      response.setCharacterEncoding("UTF-8");
      response.setContentType("application/pdf");

      Rectangle pageSize = new Rectangle(280F, 280F);
      Document document = new Document(pageSize, 10, 10, 10, 10);
      PdfWriter writer = PdfWriter.getInstance(document, response.getOutputStream());
      BaseFont baseFont = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",
          BaseFont.NOT_EMBEDDED);
      Font font = new Font(baseFont, 11, Font.NORMAL);
      Font font2 = new Font(baseFont, 8, Font.NORMAL);
      Font font3 = new Font(baseFont, 9, Font.NORMAL);

      InputStream pngInStream = Thread.currentThread().getContextClassLoader()
          .getResourceAsStream("images/ccs.png");
      ByteArrayOutputStream pngout = new ByteArrayOutputStream();
      byte[] buffer = new byte[1024];
      int n;
      while ((n = pngInStream.read(buffer)) != -1) {
        pngout.write(buffer, 0, n);
      }

      ServletOutputStream out = response.getOutputStream();

      document.open();
      int columnHeight = 25;

      for (LabelVO item : param) {

        int startCount = item.getStartCount();
        int endCount = item.getEndCount();

        LabelVO data;
        for (int i = startCount; i <= endCount; i++) {
          data = new LabelVO();
          BeanUtils.copyProperties(item, data);

          data.setLotNo(item.getLotNo() + "-" + i);
          data.setPackageSn(packageNoUtil.getPackageNo());

          PdfPTable table1 = new PdfPTable(1);
          //        table1.setWidths(new int[]{15, 200});
          table1.setKeepTogether(true);

          PdfPCell cell0 = new PdfPCell();

          Paragraph p = new Paragraph(Const.LABEL_TITLE, font);

          p.setAlignment(1);
          p.setSpacingBefore(15f);
          cell0.setHorizontalAlignment(PdfPCell.ALIGN_CENTER);
          cell0.setVerticalAlignment(PdfPCell.ALIGN_MIDDLE);//error
          cell0.setPaddingTop(-2f);//把字垂直居中
          cell0.setPaddingBottom(8f);//把字垂直居中
          cell0.addElement(p);
          cell0.setBorder(0);

          cell0.setNoWrap(true);
          table1.setWidthPercentage(100);
          //        table1.setKeepTogether(true);
          table1.addCell(cell0);
          table1.setSpacingBefore(20);
          PdfPTable pTable = new PdfPTable(table1);
          document.add(pTable);

          PdfPTable table = new PdfPTable(4);

          table.setWidthPercentage(100);

          table.setTotalWidth(100);
          table.setKeepTogether(true);

          table.setWidths(new int[]{60, 80, 50, 70});

          //line 1

          Paragraph materialName = new Paragraph("产品品名", font);
          PdfPCell cellTemp = new PdfPCell(materialName);
          cellTemp.setFixedHeight(columnHeight);
          table.addCell(cellTemp);

          Paragraph name = new Paragraph(item.getMaterialName(), font);
          cellTemp = new PdfPCell(name);
          cellTemp.setFixedHeight(columnHeight);
          table.addCell(cellTemp);

          Paragraph materialSn = new Paragraph("物料编码", font);

          cellTemp = new PdfPCell(materialSn);
          cellTemp.setFixedHeight(columnHeight);
          table.addCell(cellTemp);

          Paragraph materialSN = new Paragraph(item.getMaterialSN(), font);

          cellTemp = new PdfPCell(materialSN);
          cellTemp.setFixedHeight(columnHeight);
          table.addCell(cellTemp);

          document.add(table);

          table = new PdfPTable(2);
          table.setWidths(new int[]{60, 200});
          table.setWidthPercentage(100);
          table.setSpacingBefore(10);
          table.setTotalWidth(100);
          table.setKeepTogether(true);

          table.setSpacingBefore(0);
          Paragraph typeName = new Paragraph("产品型号", font);
          cellTemp = new PdfPCell(typeName);
          cellTemp.setFixedHeight(columnHeight);
          table.addCell(cellTemp);

          Paragraph typeNameValue = new Paragraph(item.getModel(), font);
          cellTemp = new PdfPCell(typeNameValue);
          cellTemp.setFixedHeight(columnHeight);
          table.addCell(cellTemp);

          Paragraph length = new Paragraph("LotNo", font);

          cellTemp = new PdfPCell(length);
          cellTemp.setFixedHeight(columnHeight);
          table.addCell(cellTemp);

          Paragraph lengthValue = new Paragraph(data.getLotNo(), font);
          cellTemp = new PdfPCell(lengthValue);
          cellTemp.setFixedHeight(columnHeight);
          table.addCell(cellTemp);

          document.add(table);

          table = new PdfPTable(4);

          table.setWidthPercentage(100);

          table.setTotalWidth(100);
          table.setKeepTogether(true);

          table.setWidths(new int[]{60, 80, 50, 70});

          Paragraph labelName = new Paragraph("入库日期", font);

          cellTemp = new PdfPCell(labelName);
          cellTemp.setFixedHeight(columnHeight);
          table.addCell(cellTemp);

          Paragraph labelNameValue = new Paragraph(DateUtil.getDay(), font);

          cellTemp = new PdfPCell(labelNameValue);
          cellTemp.setFixedHeight(columnHeight);
          table.addCell(cellTemp);

          labelName = new Paragraph("责任人", font);

          cellTemp = new PdfPCell(labelName);
          cellTemp.setFixedHeight(columnHeight);
          table.addCell(cellTemp);

          labelNameValue = new Paragraph("", font);

          cellTemp = new PdfPCell(labelNameValue);
          cellTemp.setFixedHeight(columnHeight);
          table.addCell(cellTemp);

          labelName = new Paragraph("领用日期", font);

          cellTemp = new PdfPCell(labelName);
          cellTemp.setFixedHeight(columnHeight);
          table.addCell(cellTemp);

          labelNameValue = new Paragraph("", font);

          cellTemp = new PdfPCell(labelNameValue);
          cellTemp.setFixedHeight(columnHeight);
          table.addCell(cellTemp);

          labelName = new Paragraph("责任人", font);

          cellTemp = new PdfPCell(labelName);
          cellTemp.setFixedHeight(columnHeight);
          table.addCell(cellTemp);

          labelNameValue = new Paragraph("", font);

          cellTemp = new PdfPCell(labelNameValue);
          cellTemp.setFixedHeight(columnHeight);
          table.addCell(cellTemp);

          document.add(table);

          //table 3 start

          PdfPTable table3 = new PdfPTable(2);
          table3.setWidthPercentage(100);
          table3.setTotalWidth(260);
          table3.setKeepTogether(true);
          table3.setSpacingBefore(0);
          table3.setWidths(new int[]{140, 120});

          PdfPTable table2 = new PdfPTable(2);
          table2.setWidthPercentage(100);
          table2.setTotalWidth(140);
          table2.setKeepTogether(true);
          table2.setSpacingBefore(0);
          //line 3
          table2.setWidths(new int[]{60, 80});

          //line 5

          labelName = new Paragraph("开封日期", font);

          cellTemp = new PdfPCell(labelName);
          cellTemp.setFixedHeight(columnHeight);
          table2.addCell(cellTemp);

          labelNameValue = new Paragraph("", font);

          cellTemp = new PdfPCell(labelNameValue);
          cellTemp.setFixedHeight(columnHeight);
          table2.addCell(cellTemp);

          labelName = new Paragraph("等级", font);

          cellTemp = new PdfPCell(labelName);
          cellTemp.setFixedHeight(columnHeight);
          table2.addCell(cellTemp);

          labelNameValue = new Paragraph(item.getLevel(), font);

          cellTemp = new PdfPCell(labelNameValue);
          cellTemp.setFixedHeight(columnHeight);
          table2.addCell(cellTemp);

          //line 1
          Paragraph specTitle = new Paragraph("数量", font);

          cellTemp = new PdfPCell(specTitle);
          cellTemp.setFixedHeight(columnHeight);
          table2.addCell(cellTemp);

          Chunk c1 = new Chunk(item.getLengthQuantity(), font);
          Phrase phrase = new Phrase();
          phrase.add(c1);

          cellTemp = new PdfPCell(phrase);
          cellTemp.setHorizontalAlignment(0);
          cellTemp.setFixedHeight(columnHeight);
          table2.addCell(cellTemp);

          labelName = new Paragraph("备注", font);

          cellTemp = new PdfPCell(labelName);
          cellTemp.setFixedHeight(columnHeight);
          table2.addCell(cellTemp);

          labelNameValue = new Paragraph(item.getRemark(), font2);

          cellTemp = new PdfPCell(labelNameValue);
          cellTemp.setFixedHeight(columnHeight);
          table2.addCell(cellTemp);

          PdfPCell cell = new PdfPCell(table2);
          cell.setPadding(0);
          table3.addCell(cell);
          int width = 100;
          int height = 100;
          String contents = "RBZ#" + data.getPackageSn()  //包装编号
              + "$$" + item.getMaterialSN()
              + "$$" + data.getLotNo();

          contents = new String(contents.getBytes("UTF-8"), "ISO-8859-1");

          // HashMap<EncodeHintType, Object> map = new HashMap<EncodeHintType, Object>();
          // map.put(EncodeHintType.CHARACTER_SET, "UTF-8");
          // map.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
          // map.put(EncodeHintType.MARGIN, 2);

          // MatrixToImageConfig con = new MatrixToImageConfig(Color.BLACK.getRGB(),
          //     Color.WHITE.getRGB());

          // BitMatrix bitMatrix =
          //     new MultiFormatWriter().encode(contents, BarcodeFormat.DATA_MATRIX, width, height,
          //         map);

          // BufferedImage codeImage = MatrixToImageWriter.toBufferedImage(bitMatrix, con);

          // ByteArrayOutputStream fos = new ByteArrayOutputStream();
          // ImageIO.write(codeImage, "jpg", fos);

          // Image cellimg = Image.getInstance(fos.toByteArray());

          // PdfPCell cell4 = new PdfPCell(cellimg, true);
          // Generate QR code
          QRCodeWriter qrCodeWriter = new QRCodeWriter();
          Map<EncodeHintType, Object> hints = new HashMap<>();
          hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
          BitMatrix bitMatrix = qrCodeWriter.encode(contents, BarcodeFormat.QR_CODE, width, height, hints);

          // Convert BitMatrix to Image
          java.awt.Image awtImage = MatrixToImageWriter.toBufferedImage(bitMatrix);
          Image image = Image.getInstance(awtImage, null);

          PdfPCell cell4 = new PdfPCell(image, true);
          cell4.setFixedHeight(100);
          cell4.setPaddingTop(1);
          cell4.setPaddingBottom(1);
          cell4.setPaddingLeft(1);
          cell4.setPaddingRight(1);

          cell4.setHorizontalAlignment(Element.ALIGN_CENTER);
          cell4.setVerticalAlignment(Element.ALIGN_MIDDLE);

          table3.addCell(cell4);
          document.add(table3);

          // 回写明道云
          mingDaoApiService.addPackage(data);
          lotNoUtil.addLotNo(data.getLotNo());
        }

      }
      document.close();

      writer.flush();
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  /**
   * 打印片材标签
   *
   * @param param
   * @param response
   */
  void printSheetLabel(List<LabelVO> param, HttpServletResponse response) {
    try {
      response.setCharacterEncoding("UTF-8");
      response.setContentType("application/pdf");
      Rectangle pageSize = new Rectangle(280F, 280F);
      Document document = new Document(pageSize, 10, 10, 10, 10);
      PdfWriter writer = PdfWriter.getInstance(document, response.getOutputStream());
      BaseFont baseFont = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",
          BaseFont.NOT_EMBEDDED);
      Font font = new Font(baseFont, 11, Font.NORMAL);
      InputStream pngInStream = Thread.currentThread().getContextClassLoader()
          .getResourceAsStream("images/ccs.png");
      ByteArrayOutputStream pngout = new ByteArrayOutputStream();
      byte[] buffer = new byte[1024];
      int n;
      while ((n = pngInStream.read(buffer)) != -1) {
        pngout.write(buffer, 0, n);
      }
      ServletOutputStream out = response.getOutputStream();

      document.open();
      int columnHeight = 25;
      for (LabelVO item : param) {
        item.setPackageSn(packageNoUtil.getPackageNo());

        PdfPTable table1 = new PdfPTable(1);
        table1.setKeepTogether(true);
        PdfPCell cell0 = new PdfPCell();
        Paragraph p = new Paragraph(Const.LABEL_TITLE, font);
        p.setAlignment(1);
        p.setSpacingBefore(15f);
        cell0.setHorizontalAlignment(PdfPCell.ALIGN_CENTER);
        cell0.setVerticalAlignment(PdfPCell.ALIGN_MIDDLE);
        cell0.setPaddingTop(-2f);//把字垂直居中
        cell0.setPaddingBottom(8f);//把字垂直居中
        cell0.addElement(p);
        cell0.setBorder(0);
        cell0.setNoWrap(true);
        table1.setWidthPercentage(100);
        table1.addCell(cell0);
        table1.setSpacingBefore(20);
        PdfPTable pTable = new PdfPTable(table1);
        document.add(pTable);
        PdfPTable table = new PdfPTable(4);

        table.setWidthPercentage(100);

        table.setTotalWidth(100);
        table.setKeepTogether(true);

        table.setWidths(new int[]{60, 80, 50, 70});

        //line 1

        Paragraph materialName = new Paragraph("品名", font);
        PdfPCell cellTemp = new PdfPCell(materialName);
        cellTemp.setFixedHeight(columnHeight);
        table.addCell(cellTemp);
        Paragraph name = new Paragraph(item.getMaterialName(), font);
        cellTemp = new PdfPCell(name);
        cellTemp.setFixedHeight(columnHeight);
        table.addCell(cellTemp);

        Paragraph materialSn = new Paragraph("物料编码", font);
        cellTemp = new PdfPCell(materialSn);
        cellTemp.setFixedHeight(columnHeight);
        table.addCell(cellTemp);
        Paragraph materialSN = new Paragraph(item.getMaterialSN(), font);
        cellTemp = new PdfPCell(materialSN);
        cellTemp.setFixedHeight(columnHeight);
        table.addCell(cellTemp);

        document.add(table);

        table = new PdfPTable(2);
        table.setWidths(new int[]{60, 200});
        table.setWidthPercentage(100);
        table.setSpacingBefore(10);
        table.setTotalWidth(100);
        table.setKeepTogether(true);
        table.setSpacingBefore(0);

        Paragraph typeName = new Paragraph("规格", font);
        cellTemp = new PdfPCell(typeName);
        cellTemp.setFixedHeight(columnHeight);
        table.addCell(cellTemp);
        Paragraph typeNameValue = new Paragraph(item.getModel(), font);
        cellTemp = new PdfPCell(typeNameValue);
        cellTemp.setFixedHeight(columnHeight);
        table.addCell(cellTemp);

        /*Paragraph client = new Paragraph("客户", font);
        cellTemp = new PdfPCell(client);
        cellTemp.setFixedHeight(columnHeight);
        table.addCell(cellTemp);
        Paragraph clientValue = new Paragraph(data.getString(PackageConst.CLIENT), font);
        cellTemp = new PdfPCell(clientValue);
        cellTemp.setFixedHeight(columnHeight);
        table.addCell(cellTemp);*/

        Paragraph client = new Paragraph("Lot No", font);
        cellTemp = new PdfPCell(client);
        cellTemp.setFixedHeight(columnHeight);
        table.addCell(cellTemp);
        Paragraph clientValue = new Paragraph(item.getLotNo(), font);
        cellTemp = new PdfPCell(clientValue);
        cellTemp.setFixedHeight(columnHeight);
        table.addCell(cellTemp);

        Paragraph orderNumber = new Paragraph("订单号", font);
        cellTemp = new PdfPCell(orderNumber);
        cellTemp.setFixedHeight(columnHeight);
        table.addCell(cellTemp);
        Paragraph orderNumberValue = new Paragraph(item.getOrderNo(), font);
        cellTemp = new PdfPCell(orderNumberValue);
        cellTemp.setFixedHeight(columnHeight);
        table.addCell(cellTemp);

        document.add(table);

        table = new PdfPTable(4);

        table.setWidthPercentage(100);

        table.setTotalWidth(100);
        table.setKeepTogether(true);
        table.setWidths(new int[]{60, 80, 50, 70});

        Paragraph createDate = new Paragraph("生产日期", font);
        cellTemp = new PdfPCell(createDate);
        cellTemp.setFixedHeight(columnHeight);
        table.addCell(cellTemp);
        // data.getProduceDate() 不存在就取packageSn前8位
        if (StringUtils.isEmpty(item.getProduceDate())) {
          item.setProduceDate(DateUtil.formatDateString(item.getPackageSn().substring(0, 8)));
        }
//        Paragraph createDateValue = new Paragraph(DateUtil.getDay(), font);
        Paragraph createDateValue = new Paragraph(item.getProduceDate(), font);
        cellTemp = new PdfPCell(createDateValue);
        cellTemp.setFixedHeight(columnHeight);
        table.addCell(cellTemp);

        Paragraph quantity = new Paragraph("片数", font);
        cellTemp = new PdfPCell(quantity);
        cellTemp.setFixedHeight(columnHeight);
        table.addCell(cellTemp);

        // 片数对应包装数量
        item.setLengthQuantity(item.getPieceCount());

        Paragraph quantityValue = new Paragraph(item.getPieceCount(), font);
        cellTemp = new PdfPCell(quantityValue);
        cellTemp.setFixedHeight(columnHeight);
        table.addCell(cellTemp);

        document.add(table);

        PdfPTable table2 = new PdfPTable(2);
        table2.setWidthPercentage(100);
        table2.setTotalWidth(140);
        table2.setKeepTogether(true);
        table2.setSpacingBefore(0);
        table2.setWidths(new int[]{60, 80});

        Paragraph machineType = new Paragraph("机种", font);
        cellTemp = new PdfPCell(machineType);
        cellTemp.setFixedHeight(columnHeight);
        table2.addCell(cellTemp);
        Paragraph machineTypeValue = new Paragraph(item.getMachineType(), font);
        cellTemp = new PdfPCell(machineTypeValue);
        cellTemp.setFixedHeight(columnHeight);
        table2.addCell(cellTemp);

        Paragraph level = new Paragraph("等级", font);
        cellTemp = new PdfPCell(level);
        cellTemp.setFixedHeight(columnHeight);
        table2.addCell(cellTemp);
        Paragraph levelValue = new Paragraph(item.getLevel(), font);
        cellTemp = new PdfPCell(levelValue);
        cellTemp.setFixedHeight(columnHeight);
        table2.addCell(cellTemp);

        Paragraph remark = new Paragraph("备注", font);
        cellTemp = new PdfPCell(remark);
        cellTemp.setUseAscender(true);//设置居中
        cellTemp.setUseDescender(true);
        cellTemp.setVerticalAlignment(Element.ALIGN_MIDDLE);
        cellTemp.setFixedHeight(columnHeight);
        table2.addCell(cellTemp);
        Paragraph remarkValue = new Paragraph(item.getRemark(), font);
        cellTemp = new PdfPCell(remarkValue);
        cellTemp.setUseAscender(true);
        cellTemp.setUseDescender(true);
        cellTemp.setVerticalAlignment(Element.ALIGN_MIDDLE);
        cellTemp.setFixedHeight(columnHeight);
        table2.addCell(cellTemp);
        PdfPCell cell = new PdfPCell(table2);
        cell.setPadding(0);
        PdfPTable table3 = new PdfPTable(2);
        table3.setWidthPercentage(100);
        table3.setTotalWidth(260);
        table3.setKeepTogether(true);
        table3.setSpacingBefore(0);
        table3.setWidths(new int[]{140, 120});

        cell.setPadding(0);
        table3.addCell(cell);
        int width = 100;
        int height = 100;
        String contents = "RBZ#" + item.getPackageSn()  //包装编号
            + "$$" + item.getMaterialSN() + "$$" + item.getLotNo();

        contents = new String(contents.getBytes("UTF-8"), "ISO-8859-1");

        // HashMap<EncodeHintType, Object> map = new HashMap<EncodeHintType, Object>();
        // map.put(EncodeHintType.CHARACTER_SET, "UTF-8");
        // map.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.L);
        // map.put(EncodeHintType.MARGIN, 1);
        // map.put(EncodeHintType.DATA_MATRIX_SHAPE, SymbolShapeHint.FORCE_SQUARE);

        // MatrixToImageConfig con = new MatrixToImageConfig(Color.BLACK.getRGB(),
        //     Color.WHITE.getRGB());

        // BitMatrix bitMatrix =
        //     new MultiFormatWriter().encode(contents, BarcodeFormat.DATA_MATRIX, width, height, map);

        // BufferedImage codeImage = MatrixToImageWriter.toBufferedImage(bitMatrix, con);

        // ByteArrayOutputStream fos = new ByteArrayOutputStream();
        // ImageIO.write(codeImage, "jpg", fos);

        // Image cellimg = Image.getInstance(fos.toByteArray());
        // cellimg.scaleToFit(width, height);

        // PdfPCell cell4 = new PdfPCell(cellimg, true);
        // Generate QR code
        QRCodeWriter qrCodeWriter = new QRCodeWriter();
        Map<EncodeHintType, Object> hints = new HashMap<>();
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
        BitMatrix bitMatrix = qrCodeWriter.encode(contents, BarcodeFormat.QR_CODE, width, height, hints);

        // Convert BitMatrix to Image
        java.awt.Image awtImage = MatrixToImageWriter.toBufferedImage(bitMatrix);
        Image image = Image.getInstance(awtImage, null);

        PdfPCell cell4 = new PdfPCell(image, true);
        cell4.setFixedHeight(100);
        cell4.setPaddingTop(1);
        cell4.setPaddingBottom(1);
        cell4.setPaddingLeft(1);
        cell4.setPaddingRight(1);

        cell4.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell4.setVerticalAlignment(Element.ALIGN_MIDDLE);

        table3.addCell(cell4);
        document.add(table3);

        // 回写明道云
        mingDaoApiService.addPackage(item);
      }
      lotNoUtil.addLotNo(param.get(0).getLotNo());
      document.close();
      writer.flush();

    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  /**
   * 打印客户标签
   *
   * @param param
   * @param response
   */
  void printClientLabel(List<LabelVO> param, HttpServletResponse response) {
    try {
      response.setCharacterEncoding("UTF-8");
      response.setContentType("application/pdf");

      Rectangle pageSize = new Rectangle(280F, 280F);
      Document document = new Document(pageSize, 10, 10, 10, 10);
      PdfWriter writer = PdfWriter.getInstance(document, response.getOutputStream());
      BaseFont baseFont = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",
          BaseFont.NOT_EMBEDDED);
      Font font = new Font(baseFont, 11, Font.NORMAL);
      Font font2 = new Font(baseFont, 9, Font.NORMAL);
      Font font3 = new Font(baseFont, 9, Font.NORMAL);

      Font producttypeFont = new Font(baseFont, 11, Font.NORMAL);

      ServletOutputStream out = response.getOutputStream();

      document.open();
      int columnHeight = 20;

      for (LabelVO item : param) {
        int packageCount = item.getPackageCount();

        // lotNo不能重复 (redis 首次一次性拉取)
        if (lotNoUtil.isExistLotNo(item.getLotNo())) {
          return;
        }

        String packageNo = packageNoUtil.getPackageNo();
        List<LabelVO> labelList = new ArrayList<>();
        for (int i = 0; i < packageCount; i++) {
          LabelVO data = new LabelVO();
          BeanUtils.copyProperties(item, data);

          data.setPackageSn(packageNo);

          PdfPTable table1 = new PdfPTable(1);
          table1.setKeepTogether(true);

          PdfPCell cell0 = new PdfPCell();

          Paragraph p = new Paragraph(Const.LABEL_TITLE, font);

          p.setAlignment(1);
          p.setSpacingBefore(15f);
          cell0.setHorizontalAlignment(PdfPCell.ALIGN_CENTER);
          cell0.setVerticalAlignment(PdfPCell.ALIGN_MIDDLE);//error
          cell0.setPaddingTop(-2f);//把字垂直居中
          cell0.setPaddingBottom(8f);//把字垂直居中
          cell0.addElement(p);
          cell0.setBorder(0);

          cell0.setNoWrap(true);
          table1.setWidthPercentage(100);
          //        table1.setKeepTogether(true);
          table1.addCell(cell0);
          table1.setSpacingBefore(20);
          PdfPTable pTable = new PdfPTable(table1);
          document.add(pTable);

          if ("窗膜".equalsIgnoreCase(data.getMaterialName())) {

            PdfPTable table = new PdfPTable(2);
            table.setWidths(new int[]{60, 200});
            table.setWidthPercentage(100);
            table.setSpacingBefore(10);
            table.setTotalWidth(100);
            table.setKeepTogether(true);
            Paragraph materialName = new Paragraph("产品品名", font);
            PdfPCell cellTemp = new PdfPCell(materialName);
            cellTemp.setFixedHeight(columnHeight);
            table.addCell(cellTemp);

            Paragraph name = new Paragraph(data.getMaterialName(), font);
            cellTemp = new PdfPCell(name);
            cellTemp.setFixedHeight(columnHeight);
            table.addCell(cellTemp);

            table.setSpacingBefore(0);
            Paragraph typeName = new Paragraph("产品型号", font);
            cellTemp = new PdfPCell(typeName);
            cellTemp.setFixedHeight(columnHeight);
            table.addCell(cellTemp);

            Paragraph typeNameValue = new Paragraph(data.getModel(), font);
            cellTemp = new PdfPCell(typeNameValue);
            cellTemp.setFixedHeight(columnHeight);
            table.addCell(cellTemp);
            document.add(table);

            table = new PdfPTable(4);

            table.setWidthPercentage(100);

            table.setTotalWidth(100);
            table.setKeepTogether(true);

            table.setWidths(new int[]{60, 80, 50, 70});

            //line 1
            Paragraph specTitle = new Paragraph("宽幅", font);

            cellTemp = new PdfPCell(specTitle);
            cellTemp.setFixedHeight(columnHeight);
            table.addCell(cellTemp);

            Chunk c1 = new Chunk(data.getWidth(), font);
            Chunk c2 = new Chunk("   mm", font);
            Phrase phrase = new Phrase();
            phrase.add(c1);
            phrase.add(c2);

            cellTemp = new PdfPCell(phrase);
            cellTemp.setHorizontalAlignment(0);
            cellTemp.setFixedHeight(columnHeight);
            table.addCell(cellTemp);

            Paragraph materialSn = new Paragraph("物料编码", font);

            cellTemp = new PdfPCell(materialSn);
            cellTemp.setFixedHeight(columnHeight);
            table.addCell(cellTemp);

            Paragraph materialSN = new Paragraph(data.getMaterialSN(), font);

            cellTemp = new PdfPCell(materialSN);
            cellTemp.setFixedHeight(columnHeight);
            table.addCell(cellTemp);

            document.add(table);

            //table 3 start

            PdfPTable table3 = new PdfPTable(2);
            table3.setWidthPercentage(100);
            table3.setTotalWidth(260);
            table3.setKeepTogether(true);
            table3.setSpacingBefore(0);
            table3.setWidths(new int[]{140, 120});

            PdfPTable table2 = new PdfPTable(2);
            table2.setWidthPercentage(100);
            table2.setTotalWidth(140);
            table2.setKeepTogether(true);
            table2.setSpacingBefore(0);
            //line 3
            table2.setWidths(new int[]{60, 80});

            //line 5

            Paragraph length = new Paragraph("小卷长度", font);

            cellTemp = new PdfPCell(length);
            cellTemp.setFixedHeight(columnHeight);
            table2.addCell(cellTemp);

            // 长度=数量*1000/宽幅
            String lengthStr = LengthCalculatorUtil.calculateLengthAsString(
                data.getLengthQuantity(),
                data.getWidth());

            Paragraph lengthValue = new Paragraph(lengthStr, font);

            cellTemp = new PdfPCell(lengthValue);
            cellTemp.setFixedHeight(columnHeight);
            table2.addCell(cellTemp);

            Paragraph netWeightName = new Paragraph("净        重", font);

            cellTemp = new PdfPCell(netWeightName);
            cellTemp.setFixedHeight(columnHeight);
            table2.addCell(cellTemp);

            c1 = new Chunk(data.getNetWeight(), font);
            c2 = new Chunk("   KG", font);
            phrase = new Phrase();
            phrase.add(c1);
            phrase.add(c2);

            cellTemp = new PdfPCell(phrase);
            cellTemp.setFixedHeight(columnHeight);
            table2.addCell(cellTemp);

            //table 2 line 2
            Paragraph grossWeightName = new Paragraph("毛        重", font);
            cellTemp = new PdfPCell(grossWeightName);
            cellTemp.setFixedHeight(columnHeight);
            table2.addCell(cellTemp);

            c1 = new Chunk(data.getGrossWeight(), font);
            c2 = new Chunk("   KG", font);
            phrase = new Phrase();
            phrase.add(c1);
            phrase.add(c2);

            cellTemp = new PdfPCell(phrase);
            cellTemp.setFixedHeight(columnHeight);
            table2.addCell(cellTemp);

            Paragraph dateTitle = new Paragraph("生产日期", font);
            cellTemp = new PdfPCell(dateTitle);
            cellTemp.setFixedHeight(columnHeight);
            table2.addCell(cellTemp);
            // data.getProduceDate() 不存在就取packageSn前8位
            if (StringUtils.isEmpty(item.getProduceDate())) {
              data.setProduceDate(DateUtil.formatDateString(data.getPackageSn().substring(0, 8)));
            }
            Paragraph date = new Paragraph(data.getProduceDate(), font);
            cellTemp = new PdfPCell(date);
            cellTemp.setFixedHeight(columnHeight);
            table2.addCell(cellTemp);

            PdfPCell cell = new PdfPCell(table2);
            cell.setPadding(0);
            table3.addCell(cell);
            int width = 110;
            int height = 70;

            String contents = data.getModel()//产品型号
                + "," + data.getWidth() // 宽度
                + "," + data.getLengthQuantity() // 面积
                + "," + lengthStr // 长度
                + "," + data.getLotNo() //轴号
                + "," + data.getProduceDate(); //生产日期

            contents = new String(contents.getBytes("UTF-8"), "ISO-8859-1");

            // Generate QR code
            QRCodeWriter qrCodeWriter = new QRCodeWriter();
            Map<EncodeHintType, Object> hints = new HashMap<>();
            hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
            BitMatrix bitMatrix = qrCodeWriter.encode(contents, BarcodeFormat.QR_CODE, width,
                height,
                hints);

            // Convert BitMatrix to Image
            java.awt.Image awtImage = MatrixToImageWriter.toBufferedImage(bitMatrix);
            Image image = Image.getInstance(awtImage, null);

            PdfPCell cell4 = new PdfPCell(image, true);
            cell4.setFixedHeight(80);
            cell4.setPaddingTop(1);
            cell4.setPaddingBottom(1);
            cell4.setPaddingLeft(1);
            cell4.setPaddingRight(1);

            cell4.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell4.setVerticalAlignment(Element.ALIGN_MIDDLE);

            table3.addCell(cell4);
            document.add(table3);

            table = new PdfPTable(1);
            table.setWidthPercentage(100);
            table.setSpacingBefore(0);
            table.setTotalWidth(100);
            table.setKeepTogether(true);

            c1 = new Chunk("备注", font);

            c2 = new Chunk(data.getRemark(), font);
            phrase = new Phrase();
            phrase.add(c1);
            phrase.add(Chunk.NEWLINE);
            phrase.add(c2);

            cellTemp = new PdfPCell(phrase);
            cellTemp.setFixedHeight(columnHeight * 4);
            //          cellTemp.setVerticalAlignment(PdfPCell.ALIGN_MIDDLE);
            table.addCell(cellTemp);
            document.add(table);

            labelList.add(data);

            // 回写明道云
//            mingDaoApiService.addPackage(data);

          } else {
            PdfPTable table = new PdfPTable(4);
            table.setWidths(new int[]{35, 105, 50, 70});

            table.setWidthPercentage(100);
            table.setSpacingBefore(10);
            table.setTotalWidth(100);
            table.setKeepTogether(true);
            Paragraph materialName = new Paragraph("名称", font);
            PdfPCell cellTemp = new PdfPCell(materialName);
            cellTemp.setFixedHeight(columnHeight);
            table.addCell(cellTemp);

            Paragraph name = new Paragraph(data.getMaterialName(), font);
            cellTemp = new PdfPCell(name);
            cellTemp.setFixedHeight(columnHeight);
            table.addCell(cellTemp);

            Paragraph dateTitle = new Paragraph("生产日期", font);
            cellTemp = new PdfPCell(dateTitle);
            cellTemp.setFixedHeight(columnHeight);
            table.addCell(cellTemp);
            // data.getProduceDate() 不存在就取packageSn前8位
            if (StringUtils.isEmpty(item.getProduceDate())) {
              data.setProduceDate(DateUtil.formatDateString(data.getPackageSn().substring(0, 8)));
            }
            Paragraph date = new Paragraph(data.getProduceDate(), font);
            cellTemp = new PdfPCell(date);
            cellTemp.setFixedHeight(columnHeight);
            table.addCell(cellTemp);

            document.add(table);

            table = new PdfPTable(2);

            table.setWidthPercentage(100);

            table.setTotalWidth(100);
            table.setKeepTogether(true);

            table.setWidths(new int[]{35, 225});

            //line 1
            Paragraph specTitle = new Paragraph("型号", font);

            cellTemp = new PdfPCell(specTitle);
            cellTemp.setFixedHeight(columnHeight);
            table.addCell(cellTemp);

            Paragraph spec = new Paragraph(data.getModel(), producttypeFont);

            cellTemp = new PdfPCell(spec);
            cellTemp.setFixedHeight(columnHeight);
            cellTemp.setVerticalAlignment(PdfPCell.ALIGN_MIDDLE);
            table.addCell(cellTemp);

            PdfPTable table7 = new PdfPTable(4);
            table7.setWidths(new int[]{35, 105, 50, 70});
            table7.setWidthPercentage(100);
            table7.setSpacingBefore(0);
            table7.setTotalWidth(100);

            //line2

            Paragraph thicknessName = new Paragraph("厚度", font);
            PdfPCell cell2 = new PdfPCell(thicknessName);
            cell2.setFixedHeight(columnHeight);
            table7.addCell(cell2);
            Chunk c1 = new Chunk(data.getThickness(), font);
            Chunk c2 = new Chunk("   um", font);
            Phrase phrase = new Phrase();
            phrase.add(c1);
            phrase.add(c2);

            Paragraph thickness = new Paragraph();
            thickness.add(phrase);
            thickness.setAlignment(0);

            PdfPCell cell3 = new PdfPCell(thickness);
            cell3.setFixedHeight(columnHeight);
            cell3.setHorizontalAlignment(0);
            table7.addCell(cell3);

            Paragraph materialSn = new Paragraph("物料编码", font);

            cellTemp = new PdfPCell(materialSn);
            cellTemp.setFixedHeight(columnHeight);
            table7.addCell(cellTemp);

            Paragraph materialSN = new Paragraph(data.getMaterialSN(), font);

            cellTemp = new PdfPCell(materialSN);
            cellTemp.setFixedHeight(columnHeight);
            table7.addCell(cellTemp);

            PdfPTable table5 = new PdfPTable(2);
            table5.setWidths(new int[]{35, 225});

            table5.setWidthPercentage(100);
            table5.setSpacingBefore(0);
            table5.setTotalWidth(100);
            table5.setKeepTogether(true);

            Paragraph lotNoName = new Paragraph("Lot No", font);
            PdfPCell lotNoCellTemp = new PdfPCell(lotNoName);
            cellTemp.setFixedHeight(columnHeight);
            table5.addCell(lotNoCellTemp);

            Paragraph lotNo = new Paragraph(data.getLotNo(), font2);
            font2.setSize(13);
            cellTemp = new PdfPCell(lotNo);
            cellTemp.setFixedHeight(columnHeight);
            table5.addCell(cellTemp);

            //line3
            PdfPTable table6 = new PdfPTable(4);
            table6.setWidths(new int[]{35, 105, 50, 70});

            table6.setWidthPercentage(100);
            table6.setSpacingBefore(0);
            table6.setTotalWidth(100);
            table6.setKeepTogether(true);
            Paragraph widthTitle = new Paragraph("宽幅", font);
            PdfPCell cellTemp4 = new PdfPCell(widthTitle);
            cellTemp4.setFixedHeight(columnHeight);
            table6.addCell(cellTemp4);
            //        Paragraph widthText = new Paragraph(data.getString("width"), font);
            c1 = new Chunk(data.getWidth(), font);
            c2 = new Chunk("   mm", font);
            phrase = new Phrase();
            phrase.add(c1);
            phrase.add(c2);

            cellTemp = new PdfPCell(phrase);
            cellTemp.setHorizontalAlignment(0);
            cellTemp.setFixedHeight(columnHeight);
            table6.addCell(cellTemp);

            Paragraph validPeriodTitle = new Paragraph("有效期限", font);
            cellTemp = new PdfPCell(validPeriodTitle);
            cellTemp.setFixedHeight(columnHeight);
            table6.addCell(cellTemp);
//            Paragraph purchaseNo = new Paragraph("", font);
            Paragraph purchaseNo = new Paragraph(data.getValidTime(), font);
            cellTemp = new PdfPCell(purchaseNo);
            cellTemp.setFixedHeight(columnHeight);
            table6.addCell(cellTemp);

            ///line 4

            document.add(table);
            document.add(table7);
            document.add(table5);
            document.add(table6);

            //table 3 start

            PdfPTable table3 = new PdfPTable(2);
            table3.setWidthPercentage(100);
            table3.setTotalWidth(260);
            table3.setKeepTogether(true);
            table3.setSpacingBefore(0);
            table3.setWidths(new int[]{140, 120});

            PdfPTable table2 = new PdfPTable(2);
            table2.setWidthPercentage(100);
            table2.setTotalWidth(140);
            table2.setKeepTogether(true);
            table2.setSpacingBefore(0);
            //line 3
            table2.setWidths(new int[]{35, 105});

            //line 5

            Paragraph lengthName = new Paragraph("长度", font);
            PdfPCell lengthCellName = new PdfPCell(lengthName);
            lengthCellName.setFixedHeight(columnHeight);
            table2.addCell(lengthCellName);

            // 长度=数量*1000/宽幅
            String length = LengthCalculatorUtil.calculateLengthAsString(data.getLengthQuantity(),
                data.getWidth());

            c1 = new Chunk(length, font);
            c2 = new Chunk("   m", font);
            phrase = new Phrase();
            phrase.add(c1);
            phrase.add(c2);
            PdfPCell lengthCell = new PdfPCell(phrase);
            lengthCell.setFixedHeight(columnHeight);
            lengthCell.setHorizontalAlignment(0);
            table2.addCell(lengthCell);

            Paragraph quantityName = new Paragraph("数量", font);
            PdfPCell quantityCellName = new PdfPCell(quantityName);
            quantityCellName.setFixedHeight(columnHeight);
            table2.addCell(quantityCellName);

            c1 = new Chunk(data.getLengthQuantity(), font);
            c2 = new Chunk("   M", font);
            Chunk c3 = new Chunk("2", font3);
            c3.setTextRise(4);
            phrase = new Phrase();
            phrase.add(c1);
            phrase.add(c2);
            phrase.add(c3);

            PdfPCell quantityCell = new PdfPCell(phrase);
            quantityCell.setFixedHeight(columnHeight);
            quantityCell.setHorizontalAlignment(0);
            table2.addCell(quantityCell);

            Paragraph pageSpaceName = new Paragraph("版距", font);
            cellTemp = new PdfPCell(pageSpaceName);
            cellTemp.setFixedHeight(columnHeight);
            table2.addCell(cellTemp);

            Paragraph pageSpace = new Paragraph(data.getPlateDistance(), font);
            cellTemp = new PdfPCell(pageSpace);
            cellTemp.setFixedHeight(columnHeight);
            table2.addCell(cellTemp);

            Paragraph netWeightName = new Paragraph("净重", font);

            cellTemp = new PdfPCell(netWeightName);
            cellTemp.setFixedHeight(columnHeight);
            table2.addCell(cellTemp);

            c1 = new Chunk(data.getNetWeight(), font);
            c2 = new Chunk("   KG", font);
            phrase = new Phrase();
            phrase.add(c1);
            phrase.add(c2);

            cellTemp = new PdfPCell(phrase);
            cellTemp.setFixedHeight(columnHeight);
            table2.addCell(cellTemp);

            //table 2 line 2
            Paragraph grossWeightName = new Paragraph("毛重", font);
            cellTemp = new PdfPCell(grossWeightName);
            cellTemp.setFixedHeight(columnHeight);
            table2.addCell(cellTemp);

            c1 = new Chunk(data.getGrossWeight(), font);
            c2 = new Chunk("   KG", font);
            phrase = new Phrase();
            phrase.add(c1);
            phrase.add(c2);

            cellTemp = new PdfPCell(phrase);
            cellTemp.setFixedHeight(columnHeight);
            table2.addCell(cellTemp);

            PdfPCell cell = new PdfPCell(table2);
            cell.setPadding(0);
            table3.addCell(cell);
            int width = 140;
            int height = 150;

            String contents = data.getModel() //产品型号
                + "," + data.getWidth() // 宽度
                + "," + data.getLengthQuantity() // 面积
                + "," + length // 长度
                + "," + data.getLotNo() //轴号
                + "," + data.getProduceDate(); //生产日期

            contents = new String(contents.getBytes("UTF-8"), "ISO-8859-1");

            // Generate QR code
            QRCodeWriter qrCodeWriter = new QRCodeWriter();
            Map<EncodeHintType, Object> hints = new HashMap<>();
            hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
            BitMatrix bitMatrix = qrCodeWriter.encode(contents, BarcodeFormat.QR_CODE, width,
                height,
                hints);

            // Convert BitMatrix to Image
            java.awt.Image awtImage = MatrixToImageWriter.toBufferedImage(bitMatrix);
            Image image = Image.getInstance(awtImage, null);

            PdfPCell cell4 = new PdfPCell(image, true);
            cell4.setFixedHeight(100);
            cell4.setPaddingTop(1);
            cell4.setPaddingBottom(1);
            cell4.setPaddingLeft(0);
            cell4.setPaddingRight(0);

            cell4.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell4.setVerticalAlignment(Element.ALIGN_MIDDLE);

            table3.addCell(cell4);
            document.add(table3);

            //一维码
            PdfPTable table4 = new PdfPTable(1);
            table4.setSpacingBefore(0);
            table4.setKeepTogether(true);
            table4.setWidthPercentage(100);

            String codeContents = data.getLotNo();

            if (!StringUtils.isEmpty(codeContents)) {
              Image barCellImg = Image.getInstance(generateBarCode(codeContents, 15).toByteArray());

              PdfPCell cell5 = new PdfPCell(barCellImg, true);
              cell5.setFixedHeight(20);
              cell5.setPaddingTop(2);
              cell5.setPaddingLeft(20);
              cell5.setPaddingRight(20);
              cell5.setPaddingBottom(2);
              cell5.setHorizontalAlignment(Element.ALIGN_CENTER);
              cell5.setVerticalAlignment(Element.ALIGN_MIDDLE);

              table4.addCell(cell5);
            } else {
              Paragraph emptylotNo = new Paragraph("", font);
              PdfPCell cell5 = new PdfPCell(emptylotNo);
              cell5.setFixedHeight(20);
              cell5.setPaddingTop(2);
              cell5.setPaddingLeft(20);
              cell5.setPaddingRight(20);
              cell5.setPaddingBottom(2);
              cell5.setHorizontalAlignment(Element.ALIGN_CENTER);
              cell5.setVerticalAlignment(Element.ALIGN_MIDDLE);

              table4.addCell(cell5);
            }

            document.add(table4);

            labelList.add(data);

            // 回写明道云
//            mingDaoApiService.addPackage(data);
          }
        }
        // 回写明道云
        mingDaoApiService.addPackage(labelList.get(0));
      }

      lotNoUtil.addLotNo(param.get(0).getLotNo());
      document.close();
      writer.flush();
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  public ByteArrayOutputStream generateBarCode(String contents) throws Exception {
    return generateBarCode(contents, 10);
  }

  public ByteArrayOutputStream generateBarCode(String contents, int height) throws Exception {
    HashMap<EncodeHintType, Object> map = new HashMap<EncodeHintType, Object>();
    map.put(EncodeHintType.CHARACTER_SET, "utf-8");
    map.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
    map.put(EncodeHintType.MARGIN, 2);

    MatrixToImageConfig con = new MatrixToImageConfig(Color.BLACK.getRGB(), Color.WHITE.getRGB());

    BitMatrix bitMatrix = new MultiFormatWriter().encode(contents, BarcodeFormat.CODE_128, 100,
        height, map);

    BufferedImage codeImage = MatrixToImageWriter.toBufferedImage(bitMatrix, con);

    ByteArrayOutputStream fos = new ByteArrayOutputStream();
    ImageIO.write(codeImage, "jpg", fos);
    return fos;
  }
}
