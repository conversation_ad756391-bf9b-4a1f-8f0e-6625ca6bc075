package com.rinsys.controller;

import com.rinsys.service.LabelRePrintService;
import com.rinsys.service.LabelService;
import com.rinsys.service.MingDaoApiService;
import com.rinsys.vo.LabelVO;
import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <AUTHOR>
 * @date 2024/6/18 15:11
 */
@Controller
@RequestMapping("/label")
public class LabelController {

  @Autowired
  private LabelService labelService;

  @Autowired
  private LabelRePrintService labelRePrintService;

  @Resource(name = "mingDaoApiService")
  private MingDaoApiService mingDaoApiService;

  @ResponseBody
  @RequestMapping(value = "/print")
  public void print(@RequestBody List<LabelVO> labelVO, HttpServletResponse response)
      throws Exception {
    labelService.printLabel(labelVO, response);
  }

  @ResponseBody
  @RequestMapping(value = "/rePrint")
  public void rePrint(@RequestParam(required = true) String packageRowId,
      @RequestParam(required = true) String userId, HttpServletResponse response)
      throws Exception {
    labelRePrintService.rePrintLabel(packageRowId, userId, response);
  }

  @ResponseBody
  @RequestMapping(value = "/syncLotNos")
  public void syncLotNos() throws Exception {
    mingDaoApiService.syncLotNos();
  }

  @ResponseBody
  @RequestMapping(value = "/isContainedLotNos")
  public Boolean isContainedLotNos(@RequestBody LotNoRequest request) throws Exception {
    return mingDaoApiService.getLotNos().contains(request.getLotNo());
  }

  @ResponseBody
  @RequestMapping(value = "/deleteLotNo")
  public String deleteLotNo(String lotNo) throws Exception {
    mingDaoApiService.deleteLotNo(lotNo);
    return "success";
  }

  public static class LotNoRequest {

    private String lotNo;

    public String getLotNo() {
      return lotNo;
    }

    public void setLotNo(String lotNo) {
      this.lotNo = lotNo;
    }
  }

}
