package com.rinsys.util;

/**
 * <AUTHOR>
 * @date 2024/8/8 11:02
 */

import java.math.BigDecimal;

public class LengthCalculatorUtil {

  public static String calculateLengthAsString(String quantityStr, String widthStr) {
    // 如果宽幅为空，则返回 "0"
    if (widthStr == null || widthStr.isEmpty()) {
      return quantityStr;
    }

    // 将字符串转换为BigDecimal以保证精度
    BigDecimal quantity = new BigDecimal(quantityStr);
    BigDecimal width = new BigDecimal(widthStr);

    // 计算长度
    BigDecimal length = quantity.multiply(new BigDecimal("1000"))
        .divide(width, 3, BigDecimal.ROUND_HALF_UP);

    // 将BigDecimal转换为String
    return length.toString();
  }

  public static void main(String[] args) {
    String quantityStr = "123"; // 数量
    String widthStr = "";       // 宽幅

    String lengthStr = calculateLengthAsString(quantityStr, widthStr);
    System.out.println("计算得到的长度为: " + lengthStr);
  }
}