package com.rinsys.util;

import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/8/1 17:06
 */
@Component
public class PackageNoUtil {

  public static final String PACKAGE_SN_KEY = "WMS_PACKAGE_SNS";

  @Resource
  private RedisUtil redisUtil;

  /**
   * 生成package sn, yyyymmdd500001
   * @return
   */
  public String getPackageNo() {
    String currentDate = new SimpleDateFormat("yyyyMMdd").format(new Date());
    String newPackageSn;

    List<String> packageSnList = redisUtil.getListValue(PACKAGE_SN_KEY);

    if (packageSnList != null && !packageSnList.isEmpty()) {
      Collections.sort(packageSnList);
      String lastPackageSn = packageSnList.get(packageSnList.size() - 1);

      String lastDate = lastPackageSn.substring(0, 8);
      String lastCount = lastPackageSn.substring(8);

      if (currentDate.equals(lastDate)) {
        int newCount = Integer.parseInt(lastCount) + 1;
        newPackageSn = currentDate + String.format("%06d", newCount);
      } else {
        newPackageSn = currentDate + "500001";
      }
    } else {
      newPackageSn = currentDate + "500001";
    }

    redisUtil.addToList(PACKAGE_SN_KEY, newPackageSn);

    return newPackageSn;
  }
}
