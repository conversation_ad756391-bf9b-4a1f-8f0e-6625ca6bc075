package com.rinsys.util;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import org.springframework.util.StringUtils;

/**
 * 说明：日期处理
 */
public class DateUtil {

  private final static SimpleDateFormat sdfYear = new SimpleDateFormat("yyyy");
  private final static SimpleDateFormat sdfDay = new SimpleDateFormat("yyyy-MM-dd");
  private final static SimpleDateFormat sdfDays = new SimpleDateFormat("yyyyMMdd");
  private final static SimpleDateFormat sdfTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
  private final static SimpleDateFormat sdfTimes = new SimpleDateFormat("yyyyMMddHHmmss");


  //开始时间后缀
  public final static String startTime = " 00:00:00";
  //结束时间后缀
  public final static String endTime = " 23:59:59";

  /**
   * 获取YYYY格式
   *
   * @return
   */
  public static String getSdfTimes() {
    return sdfTimes.format(new Date());
  }

  /**
   * 获取YYYY格式
   *
   * @return
   */
  public static String getYear() {
    return sdfYear.format(new Date());
  }

  /**
   * 获取YYYY-MM-DD格式
   *
   * @return
   */
  public static String getDay() {
    return sdfDay.format(new Date());
  }

  /**
   * 获取YYYYMMDD格式
   *
   * @return
   */
  public static String getDays() {
    return sdfDays.format(new Date());
  }

  /**
   * 获取YYYY-MM-DD HH:mm:ss格式
   *
   * @return
   */
  public static String getTime() {
    return sdfTime.format(new Date());
  }

  /**
   * @param s
   * @param e
   * @return boolean
   * @throws
   * @Title: compareDate
   * @Description: TODO(日期比较 ， 如果s > = e 返回true 否则返回false)
   * <AUTHOR>
   */
  public static boolean compareDate(String s, String e) {
    if (fomatDate(s) == null || fomatDate(e) == null) {
      return false;
    }
    return fomatDate(s).getTime() >= fomatDate(e).getTime();
  }

  /**
   * 格式化日期
   *
   * @return
   */
  public static Date fomatDate(String date) {
    DateFormat fmt = new SimpleDateFormat("yyyy-MM-dd");
    try {
      return fmt.parse(date);
    } catch (ParseException e) {
      e.printStackTrace();
      return null;
    }
  }

  public static Date parseDate(String date, String format) {
    if (StringUtils.isEmpty(format)) {
      format = "yyyy-MM-dd HH:mm:ss";
    }
    DateFormat fmt = new SimpleDateFormat(format);
    try {
      return fmt.parse(date);
    } catch (ParseException e) {
      e.printStackTrace();
      return null;
    }
  }

  /**
   * 校验日期是否合法
   *
   * @return
   */
  public static boolean isValidDate(String s) {
    DateFormat fmt = new SimpleDateFormat("yyyy-MM-dd");
    try {
      fmt.parse(s);
      return true;
    } catch (Exception e) {
      return false; // 如果throw java.text.ParseException或者NullPointerException，就说明格式不对
    }
  }

  /**
   * @param startTime
   * @param endTime
   * @return
   */
  public static int getDiffYear(String startTime, String endTime) {
    DateFormat fmt = new SimpleDateFormat("yyyy-MM-dd");
    try {
      int years = (int) (
          ((fmt.parse(endTime).getTime() - fmt.parse(startTime).getTime()) / (1000 * 60 * 60 * 24))
              / 365);
      return years;
    } catch (Exception e) {
      return 0;  // 如果throw java.text.ParseException或者NullPointerException，就说明格式不对
    }
  }

  /**
   * 得到n天之后的日期
   *
   * @param days
   * @return
   */
  public static String getAfterDayDate(String days) {
    int daysInt = Integer.parseInt(days);
    Calendar canlendar = Calendar.getInstance(); // java.util包
    canlendar.add(Calendar.DATE, daysInt); // 日期减 如果不够减会将月变动
    Date date = canlendar.getTime();
    SimpleDateFormat sdfd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    String dateStr = sdfd.format(date);
    return dateStr;
  }


  /**
   * 按照yyyy-MM-dd HH:mm:ss的格式，日期转字符串
   *
   * @param date
   * @return yyyy-MM-dd HH:mm:ss
   */
  public static String date2Str(Date date) {
    return date2Str(date, "yyyy-MM-dd HH:mm:ss");
  }


  /**
   * 把时间根据时、分、秒转换为时间段
   *
   * @param StrDate
   */
  public static String getTimes(String StrDate) {
    String resultTimes = "";
    SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    Date now;
    try {
      now = new Date();
      Date date = df.parse(StrDate);
      long times = now.getTime() - date.getTime();
      long day = times / (24 * 60 * 60 * 1000);
      long hour = (times / (60 * 60 * 1000) - day * 24);
      long min = ((times / (60 * 1000)) - day * 24 * 60 - hour * 60);
      long sec = (times / 1000 - day * 24 * 60 * 60 - hour * 60 * 60 - min * 60);

      StringBuffer sb = new StringBuffer();
      //sb.append("发表于：");
      if (hour > 0) {
        sb.append(hour + "小时前");
      } else if (min > 0) {
        sb.append(min + "分钟前");
      } else {
        sb.append(sec + "秒前");
      }
      resultTimes = sb.toString();
    } catch (ParseException e) {
      e.printStackTrace();
    }
    return resultTimes;
  }

  /**
   * 按照参数format的格式，日期转字符串
   *
   * @param date
   * @param format
   * @return
   */
  public static String date2Str(Date date, String format) {
    if (date != null) {
      SimpleDateFormat sdf = new SimpleDateFormat(format);
      return sdf.format(date);
    } else {
      return "";
    }
  }

  /**
   * @Description 获取两个时间之间的时间值(后面减前面)
   * <AUTHOR>
   * @Date 2020/6/12 10:55
   * @Return
   * @Param
   * @Throws Exception
   */
  public static long getTimeSub(String beginDateStr, String endDateStr) {
    long time = 0L;
    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    Date beginDate = null;
    Date endDate = null;
    try {
      beginDate = format.parse(beginDateStr);
      endDate = format.parse(endDateStr);
    } catch (ParseException e) {
      e.printStackTrace();
    }
    time = (endDate.getTime() - beginDate.getTime());
    return time;
  }


  /**
   * @Description 根据一个日期获取这个日期之后多少天之后的日期
   * <AUTHOR>
   * @Date 2019/12/1 12:44
   * @Return String
   * @Param String date 初始日期 String days 多少天之后
   * @Throws Exception
   */
  public static String getOffDayDate(String date, String days) throws Exception {
    int year = Integer.parseInt(date.substring(0, 4));
    int month = Integer.parseInt(date.substring(5, 7));
    int day = Integer.parseInt(date.substring(8, 10));
    int daysInt = Integer.parseInt(days);
    Calendar canlendar = Calendar.getInstance(); // java.util包
    canlendar.set(year, month - 1, day);
    canlendar.add(Calendar.DATE, daysInt); // 日期减 如果不够减会将月变动
    //Date newDate = canlendar.getTime();
    //String dateStr = sdfd.format(newDate);
    month = (canlendar.get(Calendar.MONTH) + 1);
    String months = "" + month;
    if (month < 10) {
      months = "0" + month;
    }
    day = canlendar.get(Calendar.DAY_OF_MONTH);
    if (day < 10) {
      days = "0" + day;
    } else {
      days = String.valueOf(day);
    }
    String dateStr = "" + canlendar.get(Calendar.YEAR) + "-" + months + "-" + days + date
        .substring(10, date.length());
    return dateStr;
  }

  public static String getAfterDayDateDay(String days) {
    int daysInt = Integer.parseInt(days);
    Calendar canlendar = Calendar.getInstance(); // java.util包
    canlendar.add(Calendar.DATE, daysInt); // 日期减 如果不够减会将月变动
    Date date = canlendar.getTime();

    SimpleDateFormat sdfd = new SimpleDateFormat("yyyy-MM-dd");
    String dateStr = sdfd.format(date);

    return dateStr;
  }

//    public static Date getDayByYearMonth(String yearMonth) {
//      String[] yearMonthStr = yearMonth.split("-");
//      if(StringUtils.isEmpty(yearMonth)) {
//        return null;
//      } else if(yearMonthStr != null && yearMonthStr.length == 2) {
//        LocalDate localDate = LocalDate.of(Integer.parseInt(yearMonthStr[0]),Integer.parseInt(yearMonthStr[1]),1);
//
//        localDate.
//
//      }
//      return null;
//		}

  /**
   * 得到n天之后是周几
   */
  public static String getAfterDayWeek(String days) {
    int daysInt = Integer.parseInt(days);
    Calendar canlendar = Calendar.getInstance(); // java.util包
    canlendar.add(Calendar.DATE, daysInt); // 日期减 如果不够减会将月变动
    Date date = canlendar.getTime();
    SimpleDateFormat sdf = new SimpleDateFormat("E");
    String dateStr = sdf.format(date);
    return dateStr;
  }


  /**
   * @Description 获取当前是周几
   * <AUTHOR>
   * @Date 2020/3/17 14:19
   * @Return
   * @Param
   * @Throws Exception
   */
  public static String getNowDayWeek() {
    Calendar canlendar = Calendar.getInstance(); // java.util包
    Date date = canlendar.getTime();
    SimpleDateFormat sdf = new SimpleDateFormat("E");
    String dateStr = sdf.format(date);
    return dateStr;
  }

  /**
   * @Description 根据Long值转换成字符串日期
   * <AUTHOR>
   * @Date 2019/5/22 13:18
   * @Return str
   * @Param Long date
   * @Throws Exception
   */
  public static String formartDate(Long date) {
    SimpleDateFormat fmt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    Date newDate = new Date(date);
    return fmt.format(newDate);
  }

  /**
   * @Description 根据一个时间获取Long值多少后的时间
   * <AUTHOR>
   * @Date 2020/9/21 14:04
   * @Return
   * @Param
   * @Throws Exception
   */
  public static String getDateAfterLong(String dateTime, long time) throws Exception {
    SimpleDateFormat fmt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    Date date = fmt.parse(dateTime);
    time = date.getTime() + time;
    Date newDate = new Date(time);
    return fmt.format(newDate);
  }

  /**
   * @Description 根据Date转换成时间字符串
   * <AUTHOR>
   * @Date 2019/5/22 13:21
   * @Return string
   * @Param Date date
   * @Throws Exception
   */
  public static String formartDate(Date date) {
    SimpleDateFormat fmt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    return fmt.format(date);
  }

  public static String formatDateString(String input ) {
    String output = input.substring(0, 4) + "-" +
            input.substring(4, 6) + "-" +
            input.substring(6, 8);
    return output;
  }

  /**
   * @Description 根据年月获取这个月所有的天数
   * <AUTHOR>
   * @Date 2019/7/15 17:47
   * @Return
   * @Param
   * @Throws Exception
   */
  public static List<String> getDayByMonth(int yearParam, int monthParam) {
    List list = new ArrayList();
    Calendar aCalendar = Calendar.getInstance(Locale.CHINA);
    aCalendar.set(yearParam, monthParam - 1, 1);
    int year = aCalendar.get(Calendar.YEAR);//年份
    int month = aCalendar.get(Calendar.MONTH) + 1;//月份
    int day = aCalendar.getActualMaximum(Calendar.DATE);
    for (int i = 1; i <= day; i++) {
      String aDate = null;
      if (month < 10 && i < 10) {
        aDate = String.valueOf(year) + "-0" + month + "-0" + i;
      }
      if (month < 10 && i >= 10) {
        aDate = String.valueOf(year) + "-0" + month + "-" + i;
      }
      if (month >= 10 && i < 10) {
        aDate = String.valueOf(year) + "-" + month + "-0" + i;
      }
      if (month >= 10 && i >= 10) {
        aDate = String.valueOf(year) + "-" + month + "-" + i;
      }
      list.add(aDate);
    }
    return list;
  }


  private static String getDateWithZero(int date) {
    if (date < 10) {
      return "0" + date;
    } else {
      return "" + date;
    }
  }

  /**
   * @Description 获取当前年份数字
   * <AUTHOR>
   * @Date 2019/11/30 11:29
   * @Return
   * @Param
   * @Throws Exception
   */
  public static int getYearNumber() {
    Calendar c = Calendar.getInstance();
    int year = c.get(Calendar.YEAR);//获取年份，需要与数据库对比
    return year;
  }

  /**
   * @Description 获取当你前月份数字
   * <AUTHOR>
   * @Date 2019/11/30 11:29
   * @Return
   * @Param
   * @Throws Exception
   */
  public static int getMonthNumber() {
    Calendar c = Calendar.getInstance();
    int month = c.get(Calendar.MONTH) + 1;
    if (month > 12) {
      month = 1;
    }
    return month;
  }

  /**
   * @Description 获取当前日期数字
   * <AUTHOR>
   * @Date 2019/11/30 11:29
   * @Return
   * @Param
   * @Throws Exception
   */
  public static int getDateNumber() {
    Calendar c = Calendar.getInstance();
    int date = c.get(Calendar.DAY_OF_MONTH);
    return date;
  }

  public static Date convertStrToDate(String date) throws Exception {
    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
    return format.parse(date);
  }


  public static String timeStamp2Date(String time) {
    Long timeLong = Long.parseLong(time);
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//要转换的时间格式
    Date date;
    try {
      date = sdf.parse(sdf.format(timeLong));
      return sdf.format(date);
    } catch (Exception e) {
      e.printStackTrace();
      return null;
    }
  }

  public static String timestamp2String(Timestamp time) {
    return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(time);
  }

  /**
   * @Description 获取n月之后的某月
   * <AUTHOR>
   * @Date 2019/12/17 11:01
   * @Return
   * @Param
   * @Throws Exception
   */
  public static String getAfterMonthToMonth(String month) {
    int monthInt = Integer.parseInt(month);
    Calendar canlendar = Calendar.getInstance(); // java.util包
    canlendar.add(Calendar.MONTH, monthInt); // 日期减 如果不够减会将月变动
    Date date = canlendar.getTime();
    SimpleDateFormat sdfd = new SimpleDateFormat("yyyy-MM");
    String dateStr = sdfd.format(date);
    return dateStr;
  }

  /**
   * @Description 获取某年某月的最大的一天
   * <AUTHOR>
   * @Date 2020/3/17 14:26
   * @Return
   * @Param
   * @Throws Exception
   */
  public static int queryMonthMax(int year, int month) {
    if (month == 1) {
      month = 12;
    } else {
      month--;
    }
    Calendar canlendar = Calendar.getInstance();
    canlendar.set(Calendar.YEAR, year);
    canlendar.set(Calendar.MONTH, month);
    return canlendar.getActualMaximum(Calendar.DATE);
  }

  /**
   * @Description 获取当前月份最大的天数
   * <AUTHOR>
   * @Date 2020-10-10 13:04
   * @Return int
   * @Param
   * @Throws Exception
   */
  public static int getMaxByToday() {
    Calendar canlendar = Calendar.getInstance();
    return canlendar.getActualMaximum(Calendar.DATE);
  }

  public static List<Map<String, String>> getWeekDayOfYear() {
    Calendar canlendar = Calendar.getInstance();
    int year = canlendar.get(Calendar.YEAR);
    //canlendar.set(Calendar.MONTH,10);
    int day = canlendar.getActualMaximum(Calendar.DATE);
    List<Integer> weekList = new ArrayList<>();
    Set<Integer> weekSet = new HashSet<>();
    for (int i = 1; i < day; i++) {
      canlendar.set(year, canlendar.get(Calendar.MONTH), i);
      int startWeek = canlendar.get(Calendar.WEEK_OF_YEAR);
      if (!weekSet.contains(startWeek)) {
        weekList.add(startWeek);
        weekSet.add(startWeek);
      }
    }
    List<Map<String, String>> dateList = new ArrayList<>();
    for (int i = 0; i < weekList.size(); i++) {
      Map<String, String> dateMap = new HashMap<>();
      if (weekList.get(i) == 1) {
        dateMap = getWeekTime((year + 1), weekList.get(i));
      } else {
        dateMap = getWeekTime(year, weekList.get(i));
      }
      dateList.add(dateMap);
    }
    return dateList;
  }

  /**
   * @Description 获取周的开始时间和结束时间
   * <AUTHOR>
   * @Date 2019/12/17 15:08
   * @Return
   * @Param
   * @Throws Exception
   */
  public static Map<String, String> getWeekTime(int year, int week) {
    Calendar canlendar = Calendar.getInstance();
    canlendar.set(Calendar.YEAR, year);
    canlendar.set(Calendar.WEEK_OF_YEAR, week - 1);
    canlendar.set(Calendar.DAY_OF_WEEK, 0);
    canlendar.add(Calendar.DAY_OF_YEAR, +1);
    String day = "";
    if (canlendar.get(Calendar.DAY_OF_MONTH) < 10) {
      day = day + "0" + canlendar.get(Calendar.DAY_OF_MONTH);
    } else {
      day = String.valueOf(canlendar.get(Calendar.DAY_OF_MONTH));
    }
    String startDate =
        canlendar.get(Calendar.YEAR) + "-" + (canlendar.get(Calendar.MONTH) + 1) + "-" + day;
    canlendar.add(Calendar.DAY_OF_WEEK, 6);
    day = "";
    if (canlendar.get(Calendar.DAY_OF_MONTH) < 10) {
      day = day + "0" + canlendar.get(Calendar.DAY_OF_MONTH);
    } else {
      day = String.valueOf(canlendar.get(Calendar.DAY_OF_MONTH));
    }
    String endDate =
        canlendar.get(Calendar.YEAR) + "-" + (canlendar.get(Calendar.MONTH) + 1) + "-" + day;
    Map<String, String> dataMap = new HashMap<>();
    dataMap.put("startDate", startDate);
    dataMap.put("endDate", endDate);
    dataMap.put("week", String.valueOf(week));
    return dataMap;
  }


  /**
   * @Description 时间戳转换成日期
   * <AUTHOR>
   * @Date 2020/6/11 15:53
   * @Return
   * @Param
   * @Throws Exception
   */
  public static String timeTransition(Long ztime) {
    Long tian = ztime / (1000 * 60 * 60 * 24);
    Long shi = (ztime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60);
    Long fen = (ztime % (1000 * 60 * 60 * 24)) % (1000 * 60 * 60) / (1000 * 60);
    if (tian > 0) {
      return tian + "天" + shi + "时" + fen + "分";
    }
    return shi + "时" + fen + "分";
  }


  /**
   * transfer long to minutes.
   *
   * @param time
   * @return
   */
  public static long timeTransToMin(Long time) {
    return time / 1000 / 60;

  }


  public static String timeTransitionTime(Long ztime) {
    Long tian = ztime / (1000 * 60 * 60 * 24);
    Long shi = (ztime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60);
    Long fen = (ztime % (1000 * 60 * 60 * 24)) % (1000 * 60 * 60) / (1000 * 60);
    Long miao = (ztime % (1000 * 60 * 60 * 24)) % (1000 * 60 * 60) % (1000 * 60) / (1000);
    if (tian > 0) {
      return tian + "天" + shi + "时" + fen + "分" + miao + "秒";
    }
    return shi + "时" + fen + "分" + miao + "秒";
  }

  /**
   * @Description 根据月份查询英文缩写
   * <AUTHOR>
   * @Date 2019/12/18 10:04
   * @Return String
   * @Param month 月份
   * @Throws Exception
   */
  public static String getMonthName(int month) {
    String monthArray[] = {"Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct",
        "Nov", "Dec"};
    return monthArray[month - 1];
  }

  public static long getMinsBetweenTwoDate(Date begin, Date end) {
    long between = (end.getTime() - begin.getTime()) / 1000;
    long min = between / 60;
    return min;
  }


  /**
   * @Description 判断是不是上午
   * <AUTHOR>
   * @Date 2020/3/17 14:32
   * @Return
   * @Param
   * @Throws Exception
   */
  public static boolean queryNowForenoon() {
    GregorianCalendar ca = new GregorianCalendar();
    int i = ca.get(GregorianCalendar.AM_PM);
    if (i == 0) {
      return true;
    }
    return false;
  }

  public static Date getAfterNMinutesDate(int minutes) {
    Calendar calendar = Calendar.getInstance();
    calendar.add(Calendar.MINUTE, minutes);
    return calendar.getTime();
  }

  /**
   * @Description 获取两个时间秒数(后面减前面)
   * <AUTHOR>
   * @Date 2020/7/13 17:02
   * @Return
   * @Param
   * @Throws Exception
   */
  public static Integer getSecondSub(String beginDateStr, String endDateStr) {
    Long second = 0L;
    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
    Date beginDate = null;
    Date endDate = null;
    try {
      beginDate = format.parse(beginDateStr);
      endDate = format.parse(endDateStr);
    } catch (ParseException e) {
      e.printStackTrace();
    }
    second = (endDate.getTime() - beginDate.getTime()) / (1000);
    return second.intValue();
  }

  /**
   * <li>功能描述：时间相减得到天数
   *
   * @param beginDateStr
   * @param endDateStr
   * @return int
   * <AUTHOR>
   */
  public static int getDaySub(String beginDateStr, String endDateStr) {
    int day = 0;
    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
    Date beginDate = null;
    Date endDate = null;
    try {
      beginDate = format.parse(beginDateStr);
      endDate = format.parse(endDateStr);
    } catch (ParseException e) {
      e.printStackTrace();
    }
    day = (int) ((endDate.getTime() - beginDate.getTime()) / (24 * 60 * 60 * 1000));
    //System.out.println("相隔的天数="+day);
    return day;
  }

  /**
   * @Description 格式化日期
   * <AUTHOR>
   * @Date 2019/5/30 10:04
   * @Return String
   * @Param Date
   * @Throws Exception
   */
  public static String formatDate(Date date) {
    DateFormat fmt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    try {
      return fmt.format(date);
    } catch (Exception e) {
      e.printStackTrace();
      return null;
    }
  }


  /**
   * @Description 获取指定日期的周一日期
   * <AUTHOR>
   * @Date 2020/9/21 10:30
   * @Return
   * @Param
   * @Throws Exception
   */
  public static String getDateForMonday(String dateStr) throws Exception {
    DateFormat fmt = new SimpleDateFormat("yyyy-MM-dd");
    Date date = fmt.parse(dateStr);
    Calendar cal = Calendar.getInstance();
    cal.setTime(date);
    // 获得当前日期是一个星期的第几天
    int dayWeek = cal.get(Calendar.DAY_OF_WEEK);
    if (1 == dayWeek) {
      cal.add(Calendar.DAY_OF_MONTH, -1);
    }
    // 设置一个星期的第一天，按中国的习惯一个星期的第一天是星期一
    cal.setFirstDayOfWeek(Calendar.MONDAY);
    // 获得当前日期是一个星期的第几天
    int day = cal.get(Calendar.DAY_OF_WEEK);
    cal.add(Calendar.DATE, cal.getFirstDayOfWeek() - day);
    return fmt.format(cal.getTime());
  }

  /**
   * 判断当前保养周期变更是否在当前月份中
   *
   * @param date
   * @param days
   * @return
   */
  public static String isInCurrentMonth(Date date, int days) {
    DateFormat fmt = new SimpleDateFormat("MM-dd");

    Calendar calendar = Calendar.getInstance();
    calendar.setTime(date);
    calendar.add(Calendar.DAY_OF_MONTH, days);

    Calendar calendar2 = Calendar.getInstance();

    if (calendar.get(Calendar.MONTH) == (calendar2.get(Calendar.MONTH))) {
      return fmt.format(calendar.getTime());
    }

    return null;
  }

  public static String beforeCurrentMonth(Date date, int days) {
    DateFormat fmt = new SimpleDateFormat("MM-dd");

    Calendar calendar = Calendar.getInstance();
    calendar.setTime(date);
    calendar.add(Calendar.DAY_OF_MONTH, days);

    Calendar calendar2 = Calendar.getInstance();
    calendar2.set(Calendar.HOUR, 00);
    calendar2.set(Calendar.MINUTE, 0);
    calendar2.set(Calendar.SECOND, 0);
    calendar2.set(Calendar.AM_PM, Calendar.AM);
//    calendar2.set(Calendar.DATE, 1);

    if (calendar.before(calendar2)) {
      return fmt.format(calendar.getTime());
    }

    return null;
  }

  /**
   * 判断当前模具保养是否未逾期
   *
   * @param date
   * @param days
   * @return
   */
  public static Date getToolingOverdue(Date date, int days) {
//    Date dateDate = DateUtil.parseDate(date,null);
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(date);
    calendar.add(Calendar.DAY_OF_MONTH, days);

    Calendar now = Calendar.getInstance();
    now.add(Calendar.DAY_OF_MONTH, -1);
    now.set(Calendar.HOUR, 11);
    now.set(Calendar.MINUTE, 59);
    now.set(Calendar.SECOND, 59);
    now.set(Calendar.AM_PM, Calendar.PM);

    if (calendar.getTime().before(now.getTime())) {
      return calendar.getTime();
    }

    return null;
  }

  public static String getLastMonth() {

    Calendar c = Calendar.getInstance();
    c.add(Calendar.MONTH, -1);
    return sdfDay.format(c.getTime());
  }

  /**
   * 校验日期是否合法
   */
  public static boolean isValidDateTime(String s) {
    DateFormat fmt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    try {
      fmt.setLenient(false);
      fmt.parse(s);
      return true;
    } catch (Exception e) {
      // 如果throw java.text.ParseException或者NullPointerException，就说明格式不对
      return false;
    }
  }

  /**
   * @Description 时间字符串转换成日期
   * <AUTHOR>
   * @Date 2020/9/25 17:13
   * @Return
   * @Param
   * @Throws Exception
   */
  public static Date formatDateTime(String date) {
    DateFormat fmt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    try {
      return fmt.parse(date);
    } catch (ParseException e) {
      e.printStackTrace();
      return null;
    }
  }


  /**
   * minutes beteen start time and end time.
   *
   * @param startTime
   * @param endTime
   * @return
   */
  public static long secondsBetweenStartAndEndTime(String startTime, String endTime) {
    long time = DateUtil.getTimeSub(startTime, endTime);
    return time / 1000;
  }

  /**
   * foramt influx date to java date .
   *
   * @param time
   * @return
   * @throws Exception
   */
  public static String formatInfluxTime(String time) throws Exception {
    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
    Date date = formatter.parse(time);
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    return sdf.format(date);
  }

  /**
   * transfer seconds to hours by 2 digital decimal
   *
   * @param seconds
   */
  public static double transferSecondsToHours(long seconds) {
    DecimalFormat df = new DecimalFormat("#0.00");
    double actTimeHour = Double.parseDouble(seconds + "") / 60 / 60;
    return actTimeHour;
  }

  /**
   * @Description 查询当前月的最大和最小日期
   * <AUTHOR>
   * @Date 2020-10-26 10:32
   * @Return
   * @Param
   * @Throws Exception
   */
  public static List<String> queryMonthMinMax() {
    List<String> list = new ArrayList<>();
    Calendar canlendar = Calendar.getInstance();
    int day = canlendar.getActualMaximum(Calendar.DATE);
    int year = canlendar.get(Calendar.YEAR);//获取年份，需要与数据库对比
    int month = canlendar.get(Calendar.MONTH) + 1;
    if (month > 12) {
      month = 1;
    }
    String date = year + "";
    if (month < 10) {
      date = date + "-0" + month;
    } else {
      date = date + "-" + month;
    }
    list.add(date + "-01");
    list.add(date + "-" + day);
    return list;
  }

  public static void main(String[] args) {

  }

}
