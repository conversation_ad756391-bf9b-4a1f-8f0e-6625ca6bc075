package com.rinsys.util;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Component;

@Component
@Configuration
public class RedisUtil {

  @Resource
  StringRedisTemplate stringRedisTemplate;

  @Resource
  RedisTemplate redisTemplate;

  private static RedisUtil redisUtil;

  @Resource
  private JedisConnectionFactory jedisConnectionFactory;

  @PostConstruct
  public void init() {
    redisUtil = this;
  }

  /**
   * @Description 判断缓存是否存在
   * <AUTHOR>
   * @Date 2019/11/28 18:16
   * @Return
   * @Param
   * @Throws Exception
   */
  public boolean queryValid(String key) {
    if (redisTemplate.hasKey(key)) {
      return true;
    }
    return false;
  }

  /**
   * @Description 发布消息
   * <AUTHOR>
   * @Date 2021/11/16 8:43
   * @Return
   * @Param String topic 消息主题 String message 消息内容
   * @Throws Exception
   */
  public void publish(String topic, Object message) {
    redisTemplate.convertAndSend(topic, message);
  }

  /**
   * @Description 根据key删除redis里的缓存数据
   * <AUTHOR>
   * @Date 2019/11/11 9:52
   * @Return
   * @Param
   * @Throws Exception
   */
  public void deleteKey(String key) {
    stringRedisTemplate.delete(key);
  }

  /**
   * @Description 根据key删除redis里的缓存数据
   * <AUTHOR>
   * @Date 2019/11/28 15:40
   * @Return
   * @Param String key
   * @Throws Exception
   */
  public void deleteKeyObject(String key) {
    redisTemplate.delete(key);
  }

  /**
   * @Description 获取缓存中的数据
   * <AUTHOR>
   * @Date 2019/11/13 10:30
   * @Return
   * @Param
   * @Throws Exception
   */
  public String getStrValue(String token) {
    if (redisUtil.stringRedisTemplate.hasKey(token)) {//判断缓存中是否存在
      String url = redisUtil.stringRedisTemplate.opsForValue().get(token);
      return url;
    }
    return "0";
  }

  /**
   * @Description 根据key获取值
   * <AUTHOR>
   * @Date 2019/11/28 15:10
   * @Return
   * @Param
   * @Throws Exception
   */
  public Object getDataObject(String key) {
    return redisTemplate.opsForValue().get(key);
  }

  /**
   * @Description 获取前缀数据
   * <AUTHOR>
   * @Date 2019/11/28 13:41
   * @Return
   * @Param String key 前缀
   * @Throws Exception
   */
  public Set<String> getKeyCollection(String key) throws Exception {
    Set<String> dataSet = redisTemplate.keys(key + "*");
    return dataSet;
  }

  public void setData(String key, Object o) {
    //用微信小程序openId作为键值放入缓存
    redisTemplate.opsForValue().set(key, o);
  }

  //需要进行数据格式转换

  /**
   * @Description 获取缓存中的数据set集合键
   * <AUTHOR>
   * @Date 2020/1/25 11:20
   * @Return Set<String>
   * @Param
   * @Throws Exception
   */
  public Set<String> getKeyListStr(String key) throws Exception {
    Set<String> keySet = stringRedisTemplate.keys(key + "_*");
    return keySet;
  }

  /**
   * @Description 往缓存中写数据，并且设置失效时间
   * <AUTHOR>
   * @Date 2020/1/20 18:35
   * @Return
   * @Param
   * @Throws Exception
   */
  public void setData(String key, Object o, int number, TimeUnit type) {
    redisTemplate.opsForValue().set(key, o, number, type);
  }

  public Object getData(String key, boolean ambiguous) {
    if (ambiguous) {
      return redisTemplate.opsForValue().get(key + "*");
    } else {
      return redisTemplate.opsForValue().get(key);
    }
  }

  /**
   * @Description 添加缓存班次
   * <AUTHOR>
   * @Date 2019/11/15 13:47
   * @Return
   * @Param
   * @Throws Exception
   */
  public void setStrData(String key, String value) {
    stringRedisTemplate.opsForValue().set(key, value);
  }

  /**
   * 添加缓存
   *
   * @param time 分钟
   */
  public void setStrData(String key, String value, long time) {
    stringRedisTemplate.opsForValue().set(key, value, time, TimeUnit.MINUTES);
  }

  /**
   * 获取缓存中的列表数据
   *
   * @param key 键
   * @return 列表数据
   */
  public List<String> getListValue(String key) {
    return redisTemplate.opsForList().range(key, 0, -1);
  }

  /**
   * 往缓存中的列表添加数据
   *
   * @param key   键
   * @param value 值
   */
  public void addToList(String key, String value) {
    redisTemplate.opsForList().rightPush(key, value);
  }

  /**
   * 移除缓存中的列表数据
   *
   * @param key   键
   * @param value 值
   */
  public void removeFromList(String key, String value) {
    redisTemplate.opsForList().remove(key, -1, value);
  }


  @Autowired(required = false)
  public void setRedisTemplate(RedisTemplate redisTemplate) {
    //设置序列化
    Jackson2JsonRedisSerializer jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer(
        Object.class);
    ObjectMapper om = new ObjectMapper();
    om.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
    om.enableDefaultTyping(ObjectMapper.DefaultTyping.NON_FINAL);
    jackson2JsonRedisSerializer.setObjectMapper(om);
    // 配置redisTemplate
    redisTemplate.setConnectionFactory(jedisConnectionFactory);
    RedisSerializer stringSerializer = new StringRedisSerializer();
    redisTemplate.setKeySerializer(stringSerializer); // key序列化
    redisTemplate.setValueSerializer(jackson2JsonRedisSerializer); // value序列化
    redisTemplate.setHashKeySerializer(stringSerializer); // Hash key序列化
    redisTemplate.setHashValueSerializer(jackson2JsonRedisSerializer); // Hash value序列化
    redisTemplate.afterPropertiesSet();
  }

}
