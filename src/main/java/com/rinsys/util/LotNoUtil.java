package com.rinsys.util;

import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/8/1 17:06
 */
@Component
public class LotNoUtil {

  public static final String LOT_NO_KEY = "WMS_LOT_NOS";

  @Resource
  private RedisUtil redisUtil;

  /**
   * 生成package sn, yyyymmdd500001
   *
   * @return
   */
  public boolean isExistLotNo(String lotNo) {

    List<String> existLotNoList = redisUtil.getListValue(LOT_NO_KEY);
    if (existLotNoList == null || existLotNoList.isEmpty()) {
      return false;
    } else if (existLotNoList.contains(lotNo)) {
      return true;
    } else {
      return false;
    }
  }

  public void addLotNo(String lotNo) {
    if (isExistLotNo(lotNo)) {
      return;
    }
    redisUtil.addToList(LOT_NO_KEY, lotNo);
  }

}
